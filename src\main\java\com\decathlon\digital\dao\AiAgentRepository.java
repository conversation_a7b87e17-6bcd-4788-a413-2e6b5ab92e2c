package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.entity.AiAgent;
import com.decathlon.digital.domain.agent.AIAgentType;
import com.decathlon.digital.domain.agent.AiServiceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI Agent Repository接口
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface AiAgentRepository extends JpaRepository<AiAgent, Long> {
    
    /**
     * 根据Agent代码查找Agent
     * 
     * @param agentCode Agent代码
     * @return Agent信息
     */
    Optional<AiAgent> findByAgentCode(String agentCode);
    
    /**
     * 查找所有激活的Agent，按排序顺序排列
     * 
     * @return 激活的Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.isActive = true ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findAllActiveOrderBySortOrder();
    
    /**
     * 根据分类ID查找激活的Agent
     * 
     * @param categoryId 分类ID
     * @return Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.category.id = :categoryId AND a.isActive = true ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findActiveByCategoryIdOrderBySortOrder(@Param("categoryId") Long categoryId);
    
    /**
     * 根据Agent类型查找激活的Agent
     * 
     * @param agentType Agent类型
     * @return Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.agentType = :agentType AND a.isActive = true ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findActiveByAgentTypeOrderBySortOrder(@Param("agentType") AIAgentType agentType);
    
    /**
     * 查找所有默认显示的Agent
     * 
     * @return 默认Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.isDefault = true AND a.isActive = true ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findDefaultAgentsOrderBySortOrder();
    
    /**
     * 根据AI服务类型查找激活的Agent
     * 
     * @param aiServiceType AI服务类型
     * @return Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.aiServiceType = :aiServiceType AND a.isActive = true ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findActiveByAiServiceTypeOrderBySortOrder(@Param("aiServiceType") AiServiceType aiServiceType);
    
    /**
     * 检查Agent代码是否存在
     * 
     * @param agentCode Agent代码
     * @return 是否存在
     */
    boolean existsByAgentCode(String agentCode);
    
    /**
     * 根据激活状态查找Agent
     * 
     * @param isActive 是否激活
     * @return Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.isActive = :isActive ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findByIsActiveOrderBySortOrder(@Param("isActive") Boolean isActive);
}
