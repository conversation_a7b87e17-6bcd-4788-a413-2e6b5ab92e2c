package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.entity.AiAgent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI Agent Repository接口
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface AiAgentRepository extends JpaRepository<AiAgent, Long> {
    
    /**
     * 根据Agent代码查找Agent
     * 
     * @param agentCode Agent代码
     * @return Agent信息
     */
    Optional<AiAgent> findByAgentCode(String agentCode);
    
    /**
     * 根据分类ID查找所有Agent（包含非激活的）
     *
     * @param categoryId 分类ID
     * @return Agent列表
     */
    @Query("SELECT a FROM AiAgent a LEFT JOIN FETCH a.category WHERE a.category.id = :categoryId ORDER BY a.sortOrder ASC, a.id ASC")
    List<AiAgent> findByCategoryIdOrderBySortOrder(@Param("categoryId") Long categoryId);
    
    
}
