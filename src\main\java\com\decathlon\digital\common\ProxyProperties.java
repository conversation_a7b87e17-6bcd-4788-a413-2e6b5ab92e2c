package com.decathlon.digital.common;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @auther <PERSON>
 * @since 2025-04-09
 */
@Data
public class ProxyProperties {

    private String host;
    private Integer port;

    public boolean needProxy() {
        return StringUtils.isNotBlank(host) && (port) != null;
    }

    public String buildProxyUrl() {
        if (needProxy()) {
            return String.format("http://%s:%s", host, port);
        }
        return null;
    }
}
