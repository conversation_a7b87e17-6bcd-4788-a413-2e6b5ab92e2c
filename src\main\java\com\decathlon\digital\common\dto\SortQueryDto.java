package com.decathlon.digital.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.NullHandling;
import org.springframework.data.domain.Sort.Order;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-04-14
 */
@Data
@NoArgsConstructor
public class SortQueryDto {

    protected String sort;
    protected Direction sortType = Direction.DESC;

    protected static final String DEFAULT_SORT = "updateTime";

    public SortQueryDto(String sort,
                        Direction sortType) {
        this.sortType = sortType;
        this.sort = sort;
    }

    public void buildSortProperty(String defaultSortProperty) {
        if (StringUtils.isBlank(sort) && StringUtils.isNotBlank(defaultSortProperty)) {
            this.sort = defaultSortProperty;
        }
    }




    /**
     * a builder to get a pageable by page information and sort information
     * <p>
     * pageNum the num of page start index is 1
     * pageSize how many content in one page
     * sortColumn the field name that you want to sort by it
     */
    public Sort buildSort(String sortProperty) {
        buildSortProperty(sortProperty);
        Order order = new Order(this.sortType, this.sort, NullHandling.NULLS_LAST);
        Sort sort = Sort.by(order);
        return sort;
    }

    public Sort buildDefaultSort() {
        return buildSort(DEFAULT_SORT);
    }

    public Sort buildDescByIdSort() {
        return buildSort("id");
    }
}
