package com.decathlon.digital.domain.function;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@NoArgsConstructor
@Data
public class BffSearchPriceDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @SerializedName("listPrice")
    private BigDecimal listPrice;

    @SerializedName("activePrice")
    private BigDecimal activePrice;

    @SerializedName("discountRate")
    private String discountRate;

    @SerializedName("displayDiscountRate")
    private boolean displayDiscountRate = true;
}
