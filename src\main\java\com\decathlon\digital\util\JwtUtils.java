package com.decathlon.digital.util;

import com.decathlon.digital.common.exception.AuthException;
import com.decathlon.digital.common.exception.SystemError;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @auther Shi Lei
 * @since 2024-12-02
 */
@Slf4j
public class JwtUtils {


    public static final String PERFIX = "Bearer ";

    @SneakyThrows
    public static JWTClaimsSet parseTokenBody(String token) {
        // 解析JWT
        String jwt = removePerfix(token);
        SignedJWT signedJWT = SignedJWT.parse(jwt);

        // 获取并打印Header

        // 获取并打印Payload
        JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
        log.debug("Payload: " + claimsSet.toJSONObject());
        return claimsSet;
    }

    @SneakyThrows
    public static Boolean isExpire(String token) {
        JWTClaimsSet jwtClaimsSet = parseTokenBody(token);
        Date expirationTime = jwtClaimsSet.getExpirationTime();
        if (expirationTime.before(new Date())) {
            return true;
        }
        return false;
    }

    public static void checkExpire(String token) {
        if (isExpire(token)) {
            throw new AuthException(SystemError.UNAUTHORIZED);
        }
    }


    public static String removePerfix(String token) {
        if (token.startsWith(PERFIX)) {
            token = token.replace(PERFIX, "");
        }
        return token;
    }

    public static String addPerfix(String token) {
        if (!token.startsWith(PERFIX)) {
            token = PERFIX.concat(token);
        }
        return token;
    }

    public static String getProfile(String accessToken) {
        return parseTokenBody(accessToken).getSubject();
    }




}
