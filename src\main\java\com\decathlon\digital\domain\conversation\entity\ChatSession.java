package com.decathlon.digital.domain.conversation.entity;

import com.decathlon.digital.domain.BaseTimeEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * @auther Shi <PERSON>
 * @since 2025-05-14
 */
@Data
@Entity
@Table(name = "chat_session", schema = "ai_agent")
public class ChatSession extends BaseTimeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String sessionId;

    private String profile;

    private String agentCode;

    private String contentSummary;

    private Boolean isDeleted =false;

}
