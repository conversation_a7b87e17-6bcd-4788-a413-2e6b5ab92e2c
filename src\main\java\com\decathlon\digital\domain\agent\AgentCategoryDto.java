package com.decathlon.digital.domain.agent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent分类DTO
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentCategoryDto {

    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 分类代码
     */
    private String categoryCode;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 分类图标
     */
    private String categoryIcon;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
}
