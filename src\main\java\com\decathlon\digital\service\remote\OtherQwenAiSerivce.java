package com.decathlon.digital.service.remote;

import com.alibaba.dashscope.app.ApplicationOutput;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.decathlon.digital.common.constant.RedisKeyConstants;
import com.decathlon.digital.domain.chat.ChatRespDto;
import io.reactivex.Flowable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * @auther Shi Lei
 * @since 2024-10-27
 */
@Service(value = "otherQwenAiSerivce")
@Slf4j
public class OtherQwenAiSerivce extends QwenAiSerivce implements AiService{


    @Value("${spring.ai.openai.api-key}")
    private String apiKey;

    public OtherQwenAiSerivce(RedisTemplate<String, String> redisTemplate) {
        super(redisTemplate);
    }


    @PostConstruct
    public void setProxy() {
        super.setProxy();
    }

    public Flowable<ChatRespDto> streamCallAi(String prompt, String appid, String webSession, Map<String, Object> bizParams) {
        ApplicationParam param = this.buildApplicationParam(prompt, appid);
        String sessionId = getSessionId(webSession);
        param.setSessionId(sessionId);
        if (bizParams != null) {
            setParam(bizParams, param);
        }
        param.setIncrementalOutput(true);

        return streamCallQwenAiByParam(webSession, param).map(this::convertChatResp);
    }


    public String getSessionId(String webSession) {
        String redisKey = RedisKeyConstants.appendRedisKey(RedisKeyConstants.SESSION_ID, webSession);
        return redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    protected ApplicationParam buildApplicationParam(String body, String appid) {
        ApplicationParam param = ApplicationParam.builder()
                .apiKey(apiKey)
                .appId(appid)
                .prompt(body)
                .build();
        return param;
    }

    public ChatRespDto convertChatResp(ApplicationResult in) {
//        log.debug("content: {}", in);
        String finishReason = Optional.ofNullable(in.getOutput())
                .map(ApplicationOutput::getFinishReason).orElse(null);
        String text = Optional.ofNullable(in.getOutput()).map(ApplicationOutput::getText).orElse(null);
        return ChatRespDto.builder().content(text).finishReason(finishReason).build();
    }
}
