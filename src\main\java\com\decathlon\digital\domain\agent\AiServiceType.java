package com.decathlon.digital.domain.agent;

/**
 * AI服务类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public enum AiServiceType {
    QWEN("通义千问"),
    DIFY("Dify平台"),
    OPENAI("OpenAI"),
    CLAUDE("Claude");

    private final String description;

    AiServiceType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
