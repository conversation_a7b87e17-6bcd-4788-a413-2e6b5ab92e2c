package com.decathlon.digital.domain.chat;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @auther <PERSON>
 * @since 2025-03-14
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatDialogueContextDto  {

    private String keyword;
    private String directlyReturnWord;
    private ChatSessionContextBo currentSession;

    public static ChatDialogueContextDto buildFromSessionContext(ChatSessionContextBo cacheContext) {
        return ChatDialogueContextDto.builder()
                .currentSession(cacheContext)
                .build();
    }
}
