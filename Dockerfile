# Version 1
# base image
# FROM acrcsz.azurecr.cn/dktcn/openjdk-base/eclipse-temurin:17-jdk-alpine-2025-04-09
FROM acrcsz.azurecr.cn/dktcn/openjdk-base/eclipse-temurin:17-jdk-alpine-stable

# maintainer
MAINTAINER <EMAIL>

# labels
LABEL org.opencontainers.image.authors=CS-AICHATBOX-CN

# expose port
EXPOSE 8780

# rename filename
COPY ./target/*.jar /

# # Env
# ENV JAVA_OPTS=""
# ENV TZ=UTC
#
# RUN echo "#!/bin/sh" > ./startup.sh
# RUN echo "echo \$TZ > /etc/TZ && ln -sf /usr/share/zoneinfo/\$TZ /etc/localtime" >> ./startup.sh
# RUN echo "echo Script to be run... && cat /startup.sh" >> ./startup.sh
# RUN echo "java -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap \$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -Dcom.decathlon.environment=PREPRODUCTION -jar /data-aggregator.jar" >> ./startup.sh
# RUN chmod +x ./startup.sh
# ENTRYPOINT sh -c ./startup.sh
