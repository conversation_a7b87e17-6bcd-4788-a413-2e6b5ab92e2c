package com.decathlon.digital.domain.agent;

import com.decathlon.digital.service.remote.AiService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther <PERSON>
 * @since 2025-05-06
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiAgentDto {

    private String agentCode;

    private String agentName;

    @JsonIgnore
    private String remoteAppId;

    private AIAgentType agentType;
    
    private AiService aiService;

}
