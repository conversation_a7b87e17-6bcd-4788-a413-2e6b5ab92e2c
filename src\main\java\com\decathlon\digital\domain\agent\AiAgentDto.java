package com.decathlon.digital.domain.agent;

import com.decathlon.digital.service.remote.AiService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI Agent DTO
 *
 * <AUTHOR> <PERSON>
 * @since 2025-05-06
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiAgentDto {

    /**
     * Agent代码
     */
    private String agentCode;

    /**
     * Agent名称
     */
    private String agentName;

    /**
     * 远程应用ID
     */
    @JsonIgnore
    private String remoteAppId;

    /**
     * Agent类型
     */
    private AIAgentType agentType;

    /**
     * AI服务实例
     */
    @JsonIgnore
    private AiService aiService;

    /**
     * Agent图标
     */
    private String agentIcon;

    /**
     * Agent描述
     */
    private String description;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 是否可用
     */
    private Boolean isActive;

    /**
     * 是否为默认显示
     */
    private Boolean isDefault;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

}
