-- 创建Agent分类表
CREATE TABLE ai_agent.agent_category (
    id BIGSERIAL PRIMARY KEY,
    category_code VARCHAR(50) NOT NULL UNIQUE,
    category_name VARCHAR(100) NOT NULL,
    category_icon VARCHAR(255),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    create_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建AI Agent表
CREATE TABLE ai_agent.ai_agent (
    id BIGSERIAL PRIMARY KEY,
    agent_code VARCHAR(50) NOT NULL UNIQUE,
    agent_name VARCHAR(100) NOT NULL,
    agent_type VARCHAR(25) NOT NULL,
    category_id BIGINT NOT NULL,
    agent_icon VARCHAR(255),
    description TEXT,
    remote_app_id VARCHAR(255),
    ai_service_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_agent_category FOREIGN KEY (category_id) REFERENCES ai_agent.agent_category(id)
);

-- 创建索引
CREATE INDEX idx_agent_category_code ON ai_agent.agent_category(category_code);
CREATE INDEX idx_agent_category_active ON ai_agent.agent_category(is_active);
CREATE INDEX idx_agent_code ON ai_agent.ai_agent(agent_code);
CREATE INDEX idx_agent_category_id ON ai_agent.ai_agent(category_id);
CREATE INDEX idx_agent_active ON ai_agent.ai_agent(is_active);
CREATE INDEX idx_agent_default ON ai_agent.ai_agent(is_default);

-- 插入默认分类数据
INSERT INTO ai_agent.agent_category (category_code, category_name, category_icon, description, sort_order) VALUES
('PRODUCTIVITY', '效率助手', 'productivity-icon.svg', '提升工作效率的AI助手', 1),
('CUSTOMER_SERVICE', '客服助手', 'customer-service-icon.svg', '客户服务相关的AI助手', 2),
('CREATIVE', '创意助手', 'creative-icon.svg', '创意和内容创作相关的AI助手', 3),
('ANALYSIS', '分析助手', 'analysis-icon.svg', '数据分析和报告相关的AI助手', 4);

-- 插入默认Agent数据
INSERT INTO ai_agent.ai_agent (
    agent_code, 
    agent_name, 
    agent_type, 
    category_id, 
    agent_icon, 
    description, 
    remote_app_id, 
    ai_service_type, 
    is_active, 
    is_default, 
    sort_order
) VALUES
(
    'hr-helper', 
    'HR助手', 
    'CHAT', 
    (SELECT id FROM ai_agent.agent_category WHERE category_code = 'PRODUCTIVITY'), 
    'hr-assistant-icon.svg', 
    '专业的人力资源助手，帮助处理HR相关问题', 
    'f61e59f4dc48412a9929a34d37e698af', 
    'QWEN', 
    true, 
    true, 
    1
),
(
    'op-helper', 
    'OP助手', 
    'CHAT', 
    (SELECT id FROM ai_agent.agent_category WHERE category_code = 'PRODUCTIVITY'), 
    'ops-assistant-icon.svg', 
    '运营助手，协助处理运营相关工作', 
    'app-x3sek6y2t3OItJDwzjhLrn8W', 
    'DIFY', 
    true, 
    false, 
    2
);

-- 添加注释
COMMENT ON TABLE ai_agent.agent_category IS 'Agent分类表';
COMMENT ON COLUMN ai_agent.agent_category.category_code IS '分类代码，唯一标识';
COMMENT ON COLUMN ai_agent.agent_category.category_name IS '分类名称';
COMMENT ON COLUMN ai_agent.agent_category.category_icon IS '分类图标路径';
COMMENT ON COLUMN ai_agent.agent_category.description IS '分类描述';
COMMENT ON COLUMN ai_agent.agent_category.sort_order IS '排序顺序';
COMMENT ON COLUMN ai_agent.agent_category.is_active IS '是否激活';

COMMENT ON TABLE ai_agent.ai_agent IS 'AI Agent配置表';
COMMENT ON COLUMN ai_agent.ai_agent.agent_code IS 'Agent代码，唯一标识';
COMMENT ON COLUMN ai_agent.ai_agent.agent_name IS 'Agent名称';
COMMENT ON COLUMN ai_agent.ai_agent.agent_type IS 'Agent类型：TASK/CHAT/CREATE';
COMMENT ON COLUMN ai_agent.ai_agent.category_id IS '所属分类ID';
COMMENT ON COLUMN ai_agent.ai_agent.agent_icon IS 'Agent图标路径';
COMMENT ON COLUMN ai_agent.ai_agent.description IS 'Agent描述';
COMMENT ON COLUMN ai_agent.ai_agent.remote_app_id IS '远程应用ID';
COMMENT ON COLUMN ai_agent.ai_agent.ai_service_type IS 'AI服务类型：QWEN/DIFY等';
COMMENT ON COLUMN ai_agent.ai_agent.is_active IS '是否可用';
COMMENT ON COLUMN ai_agent.ai_agent.is_default IS '是否为默认显示';
COMMENT ON COLUMN ai_agent.ai_agent.sort_order IS '排序顺序';
