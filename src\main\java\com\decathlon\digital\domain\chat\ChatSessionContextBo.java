package com.decathlon.digital.domain.chat;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * @auther <PERSON>
 * @since 2025-03-14
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatSessionContextBo {
    private String dsmCodeList;

    private String currentDsmCode;

    private Map<String, Object> weather;

}
