package com.decathlon.digital.dao;

import com.decathlon.digital.domain.conversation.entity.ChatConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @auther <PERSON>
 * @since 2025-05-14
 */
@Repository
public interface ChatConversationRepository extends JpaRepository<ChatConversation, Long> {
    @Query("select c from ChatConversation c where c.chatSessionId = ?1 and c.status = 'FORMAL' ")
    List<ChatConversation> findBySessionId(Long conversationId, Pageable pageable);

    @Query("update ChatConversation c set c.status = 'INVALID', c.systemUpdateTime = current_timestamp where chatSessionId = ?1")
    @Modifying
    void updateAsDeletedBySessionId(Long sessionId);

    @Query("update ChatConversation c set c.status = 'INVALID', c.systemUpdateTime = current_timestamp where c.id = ?1")
    @Modifying
    void updateAsDeletedById(Long id);

    @Query("select c from ChatConversation c where c.chatSessionId = ?1 and c.status = 'FORMAL' ")
    Page<ChatConversation> pageBySessionId(Long sessionId, Pageable pageable);

    @Query("select c from ChatConversation c where c.chatSessionId = ?1 and c.status = 'FORMAL' ")
    List<ChatConversation> listBySessionId(Long sessionId, Sort orders);

    @Query("update ChatConversation c set c.content = ?2, c.metadata = ?3, c.systemUpdateTime = current_timestamp, c.status = 'FORMAL' where c.id = ?1")
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    void updateLastMessage(Long sessionId, String content, Map<String, Object> metadata);

    @Query("select c.id from ChatConversation c where c.chatSessionId = ?1 and c.status = 'DRAFT' ")
    Optional<Long> findLastDraftIdBySessionId(Long sessionId, PageRequest pageRequest);
}
