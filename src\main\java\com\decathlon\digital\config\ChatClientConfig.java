package com.decathlon.digital.config;

/**
 * @auther <PERSON>
 * @since 2025-03-31
 */

import com.decathlon.digital.dao.ChatConversationRepository;
import com.decathlon.digital.domain.chat.PromptConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Component
@RequiredArgsConstructor
public class ChatClientConfig {

    private final ChatConversationRepository chatConversationRepository;

    @Bean
    ChatClient chatClient(ChatClient.Builder builder) {
        return builder.defaultSystem(PromptConstants.DEFAULT_SYSTEM)
                .defaultOptions(
                        OpenAiChatOptions.builder()
                                .build()
                )
                .defaultAdvisors(
                        //设置 日志
                        new SimpleLoggerAdvisor(),
                        //设置 上下文记忆

                        new MessageChatMemoryAdvisor(
                                new MyJdbcChatMemory(chatConversationRepository))
                ).build();

    }
}
