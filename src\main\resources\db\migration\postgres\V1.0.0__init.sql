DROP TABLE if exists ai_agent.chat_session;

CREATE TABLE ai_agent.chat_session (
    id bigserial NOT NULL,
	profile varchar(25) NULL,
	session_id varchar(100) NOT NULL,
	agent_code varchar(25) NULL,
	content_summary varchar(255) NULL,
	create_time timestamp(6) NOT NULL,
	update_time timestamp(6) NOT NULL,
	CONSTRAINT chat_session_pkey PRIMARY KEY (session_id)
);

DROP TABLE if exists ai_agent.chat_conversation;
CREATE TABLE ai_agent.chat_conversation (
	id bigserial NOT NULL,
    "content" Text NULL,
	session_id varchar(100) NULL,
	message_type varchar(25) NULL,
	metadata jsonb NULL,
	is_deleted bool NULL,
	system_update_time timestamp NOT NULL,
	CONSTRAINT chat_conversation_pkey PRIMARY KEY (id)
);

CREATE INDEX chat_conversation_session_id  ON ai_agent.chat_conversation (session_id);
CREATE unique INDEX chat_session_uk ON ai_agent.chat_session (profile,session_id);