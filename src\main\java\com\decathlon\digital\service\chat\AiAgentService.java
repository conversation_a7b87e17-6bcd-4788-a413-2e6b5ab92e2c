package com.decathlon.digital.service.chat;

import com.decathlon.digital.common.exception.IllegaInputException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.dao.AgentRepo;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.agent.AgentInputReqDto;
import com.decathlon.digital.domain.agent.AiAgentDto;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @auther Shi Lei
 * @since 2025-05-06
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiAgentService {

    private final AgentRepo agentRepo;

    public List<AiAgentDto> getAllAgentList() {
        return agentRepo.getAllAgents();
    }

    public Flowable<ChatRespDto> callAgentByStream(AgentInputReqDto dto) {
        AiAgentDto byCode = agentRepo.findByCode(dto.getAgentCode());
        if (byCode == null) {
            throw new IllegaInputException(SystemError.NOT_FOUND, "agentCode", dto.getAgentCode());
        }
        Map<String, Object> parameters = dto.getParameters();
        return byCode.getAiService().streamCallAi(dto.getContent(), byCode.getRemoteAppId(), dto.getChatSessionId().toString(), parameters);
    }


}
