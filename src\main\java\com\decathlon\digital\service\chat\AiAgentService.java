package com.decathlon.digital.service.chat;

import com.decathlon.digital.common.exception.IllegaInputException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.dao.AgentRepo;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.agent.AgentInputReqDto;
import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.domain.agent.AgentCategoryWithAgentsDto;
import com.decathlon.digital.domain.agent.AgentSimpleDto;
import com.decathlon.digital.domain.agent.entity.AgentCategory;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * AI Agent服务类
 *
 * <AUTHOR> Lei
 * @since 2025-05-06
 */
@Service
@RequiredArgsConstructor
public class AiAgentService {

    private final AgentRepo agentRepo;

    /**
     * 获取按分类分组的Agent列表
     *
     * @return 分类及其下属Agent的列表
     */
    public List<AgentCategoryWithAgentsDto> getAgentsGroupedByCategory() {
        // 获取所有激活的分类
        List<AgentCategory> categories = agentRepo.getAllActiveCategories();

        return categories.stream()
                .map(category -> {
                    // 获取该分类下的所有Agent（包含非激活的）
                    List<AiAgentDto> agents = agentRepo.getAllAgentsByCategory(category.getId());

                    // 转换为简化的Agent DTO
                    List<AgentSimpleDto> simpleAgents = agents.stream()
                    		.filter(AiAgentDto::getIsDefault) 
                            .map(this::convertToSimpleDto)
                            .toList();

                    return AgentCategoryWithAgentsDto.builder()
                            .categoryName(category.getCategoryName())
                            .categoryIconName(category.getCategoryIconName())
                            .agents(simpleAgents)
                            .build();
                })
                .filter(categoryWithAgents -> !categoryWithAgents.getAgents().isEmpty()) // 过滤掉没有Agent的分类
                .toList();
    }

    /**
     * 流式调用Agent
     *
     * @param dto 请求参数
     * @return 响应流
     */
    public Flowable<ChatRespDto> callAgentByStream(AgentInputReqDto dto) {
        AiAgentDto byCode = agentRepo.findByCode(dto.getAgentCode());
        if (byCode == null) {
            throw new IllegaInputException(SystemError.NOT_FOUND, "agentCode", dto.getAgentCode());
        }
        Map<String, Object> parameters = dto.getParameters();
        return byCode.getAiService().streamCallAi(dto.getContent(), byCode.getKey(), dto.getChatSessionId().toString(), parameters);
    }

    /**
     * 将Agent DTO转换为简化DTO
     *
     * @param agent Agent DTO
     * @return 简化Agent DTO
     */
    private AgentSimpleDto convertToSimpleDto(AiAgentDto agent) {
        return AgentSimpleDto.builder()
                .name(agent.getAgentName())
                .agentCode(agent.getAgentCode())
                .iconName(agent.getAgentIcon())
                .label(agent.getLabel())
                .cardBgUrl("") // 暂时为空，可以后续扩展
                .description(agent.getDescription())
                .active(agent.getIsActive())
                .build();
    }
}
