package com.decathlon.digital.service.chat;

import com.decathlon.digital.common.exception.IllegaInputException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.dao.AgentRepo;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.agent.AgentInputReqDto;
import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.domain.agent.AgentCategoryDto;
import com.decathlon.digital.domain.agent.entity.AgentCategory;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI Agent服务类
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Service
@RequiredArgsConstructor
public class AiAgentService {

    private final AgentRepo agentRepo;

    /**
     * 获取所有Agent列表
     *
     * @return Agent列表
     */
    public List<AiAgentDto> getAllAgentList() {
        return agentRepo.getAllAgents();
    }

    /**
     * 根据分类ID获取Agent列表
     *
     * @param categoryId 分类ID
     * @return Agent列表
     */
    public List<AiAgentDto> getAgentsByCategory(Long categoryId) {
        return agentRepo.getAgentsByCategory(categoryId);
    }

    /**
     * 获取默认显示的Agent列表
     *
     * @return 默认Agent列表
     */
    public List<AiAgentDto> getDefaultAgents() {
        return agentRepo.getDefaultAgents();
    }

    /**
     * 获取所有分类列表
     *
     * @return 分类列表
     */
    public List<AgentCategoryDto> getAllCategories() {
        List<AgentCategory> categories = agentRepo.getAllActiveCategories();
        return categories.stream()
                .map(this::convertCategoryToDto)
                .collect(Collectors.toList());
    }

    /**
     * 流式调用Agent
     *
     * @param dto 请求参数
     * @return 响应流
     */
    public Flowable<ChatRespDto> callAgentByStream(AgentInputReqDto dto) {
        AiAgentDto byCode = agentRepo.findByCode(dto.getAgentCode());
        if (byCode == null) {
            throw new IllegaInputException(SystemError.NOT_FOUND, "agentCode", dto.getAgentCode());
        }
        Map<String, Object> parameters = dto.getParameters();
        return byCode.getAiService().streamCallAi(dto.getContent(), byCode.getRemoteAppId(), dto.getChatSessionId().toString(), parameters);
    }

    /**
     * 将分类实体转换为DTO
     *
     * @param category 分类实体
     * @return 分类DTO
     */
    private AgentCategoryDto convertCategoryToDto(AgentCategory category) {
        return AgentCategoryDto.builder()
                .id(category.getId())
                .categoryCode(category.getCategoryCode())
                .categoryName(category.getCategoryName())
                .categoryIcon(category.getCategoryIcon())
                .description(category.getDescription())
                .sortOrder(category.getSortOrder())
                .isActive(category.getIsActive())
                .build();
    }
}
