
package com.decathlon.digital.domain.function;

import com.decathlon.digital.domain.chat.ChatInputReqDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * @auther <PERSON>
 * @since 2025-03-07
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ShopingGuidInputDto extends ChatInputReqDto {


    @Parameter(example = "2")
    private Integer productCount =2;
}
