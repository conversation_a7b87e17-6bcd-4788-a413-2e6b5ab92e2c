package com.decathlon.digital.domain.function;

import com.decathlon.digital.domain.chat.ChatRespDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * @auther <PERSON>
 * @since 2025-03-11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ShopGuideRespDto extends ChatRespDto {


    private String emotion;

    private List<ProductCardDto> productCardDto;

    private Map<String, Object> weather;

}
