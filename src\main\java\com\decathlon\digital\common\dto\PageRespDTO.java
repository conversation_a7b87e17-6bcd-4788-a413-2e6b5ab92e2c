package com.decathlon.digital.common.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.domain.Page;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

@Getter
@Setter
@ToString
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PageRespDTO<T> {

  private long total;
  private int totalPages;
  private int size;
  private int page;

  private List<T> result;

  public static <T> PageRespDTO<T> empty() {
    return new PageRespDTO<>(0L, 0, 0, 0, Collections.emptyList());
  }

  public static <T> PageRespDTO<T> of(Page<T> page) {
    return PageRespDTO.of(page, page.getContent());
  }

  public static <T, I> PageRespDTO<T> of(Page<I> page, Function<I, T> recordHandler) {
    if (page==null) {
      return PageRespDTO.empty();
    }
    List<T> list = page.getContent().stream().map(recordHandler).collect(toList());

    return PageRespDTO.of(page, list);
  }

  public static <T, I> PageRespDTO<T> of(Page<I> page, List<T> list) {
    return PageRespDTO.of(
        page.getTotalElements(),
        page.getTotalPages(),
        page.getPageable().getPageSize(),
        page.getPageable().getPageNumber() + 1,
        list);
  }

  public static <T> PageRespDTO<T> of(long total, int totalPages, int size, int page,
      List<T> result) {
    return new PageRespDTO<>(total, totalPages, size, page, result);
  }

}
