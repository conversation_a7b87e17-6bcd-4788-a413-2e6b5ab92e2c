package com.decathlon.digital.service.remote;

import lombok.extern.slf4j.Slf4j;

/**
 * @auther <PERSON>
 * @since 2025-03-24
 */
@Slf4j
public class AiRespFormat {
    private static final String JSON_TYPE = "json";

    public static String handleDifferentRespFormat(String resp) {

        if (resp.contains(JSON_TYPE)) {
            return handleJsonRespFormat(resp);
        }
        return resp;
    }

    public static String handleJsonRespFormat(String resp) {
        String str = resp.replace("```", "").replace(JSON_TYPE, "").replace("“", "\"").replace("”", "\"");
        return extractedJson(str);
    }

    private static String extractedJson(String str) {
        // 获取第一个 '{' 的位置
        int firstOpenBrace = str.indexOf('{');
        if (firstOpenBrace == -1) {
            log.error("字符串中没有 '{' 字符");
            return str;
        }

        // 获取最后一个 '}' 的位置
        int lastCloseBrace = str.lastIndexOf('}');
        if (lastCloseBrace == -1) {
            log.error("字符串中没有 '}' 字符");
            return str;
        }

        // 截取第一个 '{' 和最后一个 '}' 之间的内容
        String jsonContent = str.substring(firstOpenBrace, lastCloseBrace + 1);
        log.debug("截取的 JSON 内容是: " + jsonContent);
        return jsonContent;
    }
}
