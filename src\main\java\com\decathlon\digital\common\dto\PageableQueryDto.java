package com.decathlon.digital.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.NullHandling;
import org.springframework.data.domain.Sort.Order;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-04-14
 */
@Data
@NoArgsConstructor
public class PageableQueryDto extends SortQueryDto{

    protected Integer pageNum;

    protected Integer pageSize;
//    protected String sort;
//    protected Direction sortType = Direction.DESC;

//    protected static final String DEFAULT_SORT = "updateTime";

    public PageableQueryDto(Integer pageNum, Integer pageSize, String sort,
                            Direction sortType) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        super.sortType = sortType;
        super.sort = sort;
    }


    public Pageable addSortProperty(Order... orders) {
        List<Order> orderList = new ArrayList<>();
        if (StringUtils.isNotBlank(sort)) {
            Order originalOrder = new Order(this.sortType, this.sort);
            orderList.add(originalOrder);
        }
        orderList.addAll(List.of(orders));
        Sort sort = Sort.by(orderList);
        return PageRequest.of(this.pageNum - 1, this.pageSize, sort);
    }


    /**
     * a builder to get a pageable by page information and sort information
     * <p>
     * pageNum the num of page start index is 1
     * pageSize how many content in one page
     * sortColumn the field name that you want to sort by it
     */
    public Pageable buildPageRequest(String sortProperty) {
        Sort sort = super.buildSort(sortProperty);
        return PageRequest.of(pageNum - 1, pageSize, sort);
    }

    public Pageable buildDefault() {
        return buildPageRequest(DEFAULT_SORT);
    }
    public Pageable buildDescById() {
        return buildPageRequest("id");
    }
}
