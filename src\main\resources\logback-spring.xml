<?xml version="1.0" encoding="UTF-8" ?>
<!--scan=true:配置文件如果发生改变，将会被重新加载，默认值为true
    scanPeriod：设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
    当scan=true时，此属性生效。默认时间间隔为1分钟。
    debug：当此属性设置为true时，将打印出logback内部日志信息；实时查看logback运行状态，默认值为false。
-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <contextName>${HOSTNAME}</contextName>

    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <springProperty scope="context" name="KAFKA_HOST" source="common-log.bootstrap-servers"/>
    <springProperty scope="context" name="KAFKA_LOG_TOPIC" source="common-log.topic"/>
    <springProperty scope="context" name="LOG_LEVEL" source="common-log.root-level"/>

    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-2level [%t][traceId:%X{traceId}][%logger{36}] %msg %ex{full} %n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="kafkaAppender" class="com.github.danielwegener.logback.kafka.KafkaAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>GMT+8</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        {
                        "logger_name": "%logger{40}",
                        "thread_name": "%thread",
                        "level": "%level",
                        "instance_id": "%contextName",
                        "correlation_id": "%X{requestId}",
                        "traceId": "%X{traceId}",
                        "span": "%X{spanId}",
                        "parent": "%X{parentSpanId:-}",
                        "exportable": "%X{export:-}",
                        "module_name": "${APP_NAME}",
                        "message": "%marker%m%ex{full} - %logger - %F:%L%n"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>

        <topic>${KAFKA_LOG_TOPIC}</topic>
        <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.HostNameKeyingStrategy"/>
        <deliveryStrategy
                class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>
        <producerConfig>bootstrap.servers=${KAFKA_HOST}</producerConfig>
        <producerConfig>acks=0</producerConfig>
        <producerConfig>linger.ms=1000</producerConfig>
        <producerConfig>max.block.ms=0</producerConfig>
<!--        <producerConfig>sasl.mechanism=PLAIN</producerConfig>-->
<!--        <producerConfig>security.protocol=SASL_PLAINTEXT</producerConfig>-->
<!--        <producerConfig>sasl.jaas.config=${SASL_LOG_PROFILE}</producerConfig>-->
        <producerConfig>client.id=${HOSTNAME}-logback</producerConfig>
    </appender>

    <springProfile name="local,dev">
        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
<!--        <logger name="org.hibernate.type.descriptor.jdbc.BasicBinder" level="trace">-->
<!--            <appender-ref ref="STDOUT"/>-->
<!--        </logger>-->
<!--        <logger name="org.hibernate.jdbc.bind" level="trace">-->
<!--            <appender-ref ref="STDOUT"/>-->
<!--        </logger>-->
        <logger name="com.decathlon" level="debug" additivity="false">
            <appender-ref ref="STDOUT"/>
        </logger>
    </springProfile>

    <springProfile name="api,qua,preprod,prod">
        <root level="info">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="kafkaAppender"/>
        </root>
        <logger name="com.decathlon" level="${LOG_LEVEL}" additivity="false">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="kafkaAppender"/>
        </logger>
    </springProfile>

</configuration>
