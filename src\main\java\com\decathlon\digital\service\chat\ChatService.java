package com.decathlon.digital.service.chat;

import cn.hutool.core.date.DateUtil;
import com.decathlon.digital.common.constant.RedisKeyConstants;
import com.decathlon.digital.common.exception.IllegaInputException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.domain.chat.ChatDialogueContextDto;
import com.decathlon.digital.domain.chat.ChatGuideSumRespDto;
import com.decathlon.digital.domain.chat.ChatInputReqDto;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.chat.ChatSessionContextBo;
import com.decathlon.digital.domain.conversation.dto.ConversationPairDto;
import com.decathlon.digital.domain.function.DigitalHumanChatRespDto;
import com.decathlon.digital.domain.function.ProductCardDto;
import com.decathlon.digital.domain.function.ShopGuideRespDto;
import com.decathlon.digital.domain.function.ShopingGuidInputDto;
import com.decathlon.digital.service.remote.QwenAiSerivce;
import com.decathlon.digital.service.remote.QwenAppId;
import com.decathlon.digital.util.JacksonConverterUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

/**
 * @auther Shi Lei
 * @since 2025-03-07
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatService {

    private final QwenAiSerivce qwenAiSerivce;

    private final QwenAppId qwenAppId;


    private final DigitalHumanService digitalHumanService;

    private final ChatConversionService chatConversionService;

    private final RedisTemplate<String, String> redisTemplate;

    private final ChatClient chatClient;
    private Map<String, AtomicBoolean> interruptFlags = new ConcurrentHashMap<>();


    public ChatRespDto chatFree(ChatInputReqDto dto) {
        chatConversionService.createSessionByProfile(dto);
        ConversationPairDto conversationPair = chatConversionService.createConversationPair(dto);

        ChatResponse response = chatClient
                .prompt()
                .user(dto.getContent())
                .advisors(spec -> spec.param(CHAT_MEMORY_CONVERSATION_ID_KEY, dto.getChatSessionId())
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, dto.getContextLength()))
                .call()
                .chatResponse();
        String content = response.getResult().getOutput().getText();

        log.info("content: {}", content);
        return ChatRespDto.builder().content(content).conversationPair(conversationPair).build();
    }

    public Flux<ChatRespDto> chatFreeByStream(ChatInputReqDto dto) {
        chatConversionService.createSessionByProfile(dto);
        ConversationPairDto conversationPair = chatConversionService.createConversationPair(dto);

        String sessionId = dto.getChatSessionId().toString();
        interruptFlags.putIfAbsent(sessionId, new AtomicBoolean(false));

        Flux<ChatResponse> stream = chatClient
                .prompt()
                .user(dto.getContent())
                .advisors(spec -> spec.param(CHAT_MEMORY_CONVERSATION_ID_KEY, sessionId)
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, dto.getContextLength()))

                .stream().chatResponse();

        return stream
                .takeUntil(i -> {
                    AtomicBoolean atomicBoolean = interruptFlags.get(sessionId);
                    if (atomicBoolean != null) {
                        return atomicBoolean.get();
                    } else {
                        return false;
                    }
                })
                .doFinally(signal -> {
                    // 清理会话状态
                    interruptFlags.remove(sessionId);
                })
                .map(s -> convertChatResp(s))
                .startWith(ChatRespDto.builder().conversationPair(conversationPair).build());//设置最开始返回conversationId;

    }


    public ChatRespDto convertChatResp(ChatResponse s) {
        log.debug("content: {}", s);
        String finishReason = Optional.ofNullable(s.getResult())
                .map(x -> x.getMetadata()).map(x -> x.getFinishReason()).orElse(null);
        String text = Optional.ofNullable(s.getResult()).map(x -> x.getOutput()).map(AbstractMessage::getText).orElse(null);

        return ChatRespDto.builder().content(text).finishReason(finishReason).build();
    }

    public void interrupt(String sessionId) {
        AtomicBoolean flag = interruptFlags.get(sessionId);
        if (flag != null) {
            flag.set(true);
        } else {
            interruptFlags.put(sessionId, new AtomicBoolean(true));
        }
    }

    @SneakyThrows
    @Deprecated
    public ShopGuideRespDto shoppingGuides(ShopingGuidInputDto dto, Boolean enableSpeak) {
        Map<String, Object> bizParam = new HashMap<>();
        String gender = "女士";
        bizParam.put("gender", gender);
        //获取seesion级别的上下文缓存
        String session = dto.getChatSessionId().toString();
        ChatSessionContextBo cacheContext = getCacheChatContextBo(session);

        //创建单论对话的上下文
        ChatDialogueContextDto chatDialogueContextDto = ChatDialogueContextDto.buildFromSessionContext(cacheContext);
        String dsmCodeList = cacheContext.getDsmCodeList();
        bizParam.put("dsmList", dsmCodeList);
        String currentDsmCode = cacheContext.getCurrentDsmCode();
        bizParam.put("currentDsm", currentDsmCode);


        String inputContent = dto.getContent();
        StringBuilder sb = new StringBuilder();

        //        if (StringUtils.isNotBlank(currentDsmCode)) {
        //            sb.append("当前商品为:").append(currentDsmCode);
        //        } else if (StringUtils.isNotBlank(dsmCodeList)) {
        //            sb.append("商品列表:").append(dsmCodeList);
        //        }


        log.info("chat call ai step 1 start time: -> {}", DateUtil.now());
        String prompt1 = sb.append(inputContent).toString();
        log.info("call ai 判断意图 prompt  {}", prompt1);
        String aiResp = qwenAiSerivce.chatWithQwenAi(prompt1, qwenAppId.getDigitalHuman(), session, bizParam);
        log.info("chat call ai step 1 finish time: -> {}", DateUtil.now());
        StringBuilder promptstringBuilder = new StringBuilder();
        String emotion = "";
        try {
            DigitalHumanChatRespDto humanChatRespDto = JacksonConverterUtil.convertJsonToObject(aiResp, DigitalHumanChatRespDto.class);
            String content = humanChatRespDto.getContent();
            emotion = humanChatRespDto.getEmotion();
            if (StringUtils.isNotBlank(content)) {
                //如果有回复语句直接回复
                return responseDirectly(enableSpeak, content, emotion);
            }
            String dsmCodeJudgeStr = humanChatRespDto.getDsmCodeJudge();

            //判别当前上下文指向的dsmCode
            getCurrentDsmCode(dsmCodeJudgeStr, chatDialogueContextDto);


            //根据意图判断
            log.info("chat 拼接提示词 and call 3rd api start time: -> {}", DateUtil.now());
            deelIntentionsList(humanChatRespDto, promptstringBuilder, chatDialogueContextDto, dto);
            log.info("chat 拼接提示词 and call 3rd api finish time: -> {}", DateUtil.now());

        } catch (Exception e) {
            log.error("json parse error", e);
            return sendError(enableSpeak);
        }
        //如果需要直接返回消息
        String directlyReturnWord = chatDialogueContextDto.getDirectlyReturnWord();
        if (StringUtils.isNotBlank(directlyReturnWord)) {
            return responseDirectly(enableSpeak, directlyReturnWord, emotion);
        }


        List<ProductCardDto> searchRes = new ArrayList<>();

        //获取产品卡片
        String keyword = chatDialogueContextDto.getKeyword();
        //        searchRes = getSearchRes(keyword, dto.getProductCount(), promptstringBuilder);
        //通过ai组织语言
        String prompt = promptstringBuilder.toString();
        log.info("通过ai组织语言 prompt:  {}", prompt);

        log.info("chat call ai 组织语言 start time: -> {}", DateUtil.now());
        String respContent = "";
        try {
            String chatResp = qwenAiSerivce.chatWithQwenAi(prompt, qwenAppId.getChatShoppingGuide(), session, null);
            ChatGuideSumRespDto chatGuideSumRespDto = JacksonConverterUtil.convertJsonToObject(chatResp, ChatGuideSumRespDto.class);
            respContent = chatGuideSumRespDto.getContent();
            emotion = chatGuideSumRespDto.getEmotion();
        } catch (IOException e) {
            log.error("json parse error", e);
            return sendError(enableSpeak);
        }
        log.info("chat call ai 组织语言 finish time: -> {}", DateUtil.now());

        //把查询出来的dsm放入上下文中
        if (CollectionUtils.isNotEmpty(searchRes)) {
            dsmCodeList = StringUtils.join(searchRes.stream().map(x -> x.getDsmCode()).toList(), ",");
            cacheContext.setDsmCodeList(dsmCodeList);
            cacheContext.setCurrentDsmCode(null);
        }

        //更新并且保存上下文
        setContext(dto, cacheContext);

        //封装结果
        ShopGuideRespDto build = ShopGuideRespDto.builder().content(respContent).productCardDto(searchRes).emotion(emotion)
                .weather(cacheContext.getWeather()).build();
        sendVoice(enableSpeak, respContent);
        return build;
    }

    private void deelIntentionsList(DigitalHumanChatRespDto humanChatRespDto, StringBuilder promptstringBuilder, ChatDialogueContextDto cacheContext, ChatInputReqDto dto) {
        List<String> intentions = humanChatRespDto.getIntention();
        promptstringBuilder.append("#业务目的:\n").append(StringUtils.join(intentions, ",")).append("\n");

        String dsmCode = Optional.ofNullable(cacheContext.getCurrentSession()).map(ChatSessionContextBo::getCurrentDsmCode).orElse("");
        for (String intention : intentions) {
            switch (intention) {
                case "商品推荐"://根据关键字搜索商品
                    String keyword = humanChatRespDto.getSearchKeyword();
                    promptstringBuilder.append("#顾客想法:\n").append(dto.getContent()).append("\n");
                    cacheContext.setKeyword(keyword);
                    break;
                case "行程规划": //获取天气信息
                    Map<String, Object> weather = humanChatRespDto.getWeather();
                    cacheContext.getCurrentSession().setWeather(weather);
                    promptstringBuilder.append("#天气\n").append(weather.toString()).append("\n");
                    break;
                case "库存查询":
                    Integer stockByDsm = getStockByDsm(dsmCode);
                    promptstringBuilder.append("#库存:\n").append(stockByDsm).append("\n");
                    break;
                case "商品评价咨询":
                    //                    BaseApiResponseDto<JsonNode> search = reviewClient.search(dsmCode);
                    //                    String overview = Optional.ofNullable(search).map(x -> x.getData()).map(x -> x.get("overview")).map(x -> x.asText()).orElse(null);
                    //                    if (StringUtils.isNotBlank(overview)) {
                    //                        //直接读出评论并且返回
                    //                        promptstringBuilder.append("#用户评论总结:\n").append(overview).append("\n");
                    //                        //                        cacheContext.setDirectlyReturnWord(overview);
                    //                    }
                    break;
                case "产品介绍":
                    //                    List<DsmAggreationBO> dsmAggreationBOS = productCurdService.queryRelatedProduct(List.of(dsmCode));
                    //                    String productDetail = Optional.ofNullable(dsmAggreationBOS.get(0)).map(DsmAggreationBO::buildOnlyParamJson).orElse("");
                    //                    promptstringBuilder.append("#商品信息:\n").append(productDetail).append("\n");
                    break;
            }
        }
    }

    private static void getCurrentDsmCode(String dsmCodeJudgeStr, ChatDialogueContextDto cacheContext) {
        if (StringUtils.isNotBlank(dsmCodeJudgeStr)) {
            String[] split = dsmCodeJudgeStr.split(",");
            if (split.length < 2) {
                throw new RuntimeException("dsmCodeJudgeStr error");
            } else {
                if (Boolean.parseBoolean(split[0]) == true) {
                    Optional.ofNullable(cacheContext.getCurrentSession())
                            .ifPresent(c -> c.setCurrentDsmCode(split[1].trim()));
                } else {
                    cacheContext.setDirectlyReturnWord(split[1].trim());
                }
            }
        }
    }

    private ShopGuideRespDto responseDirectly(Boolean enableSpeak, String content, String emotion) {
        sendVoice(enableSpeak, content);
        return ShopGuideRespDto.builder().content(content).emotion(emotion).build();
    }

    private ShopGuideRespDto sendError(Boolean enableSpeak) {
        return responseDirectly(enableSpeak, "抱歉我暂时无法理解你说的内容,你可以咨询我其他问题", "common");
    }

    private Integer getStockByDsm(String dsmCode) {
        Random random = new Random();
        int i = random.nextInt(100);
        if (i < 50) {
            return 0;
        }
        return i;
    }

    //    private List<ProductCardDto> getSearchRes(String keyword, Integer count, StringBuilder promptstringBuilder) {
    //        List<ProductCardDto> list = new ArrayList<>();
    //        List<SearchResponse.ProductDTO> searchRes = new ArrayList<>();
    //        if (StringUtils.isNotBlank(keyword)) {
    //
    //            SearchRequest searchRequest = new SearchRequest();
    //            searchRequest.setPageSize(count);
    //            searchRequest.setText(keyword);
    //            String field = searchRequest.getSpecifiedField().concat(",benefits");
    //            searchRequest.setSpecifiedField(field);
    //            //            if (StringUtils.isNotBlank(gender)) {
    //            //                searchRequest.setFilters(List.of(new SearchRequest.FilterDTO("genders_zh", List.of(gender))));
    //            //            }
    //            SearchResponseBase<SearchResponse> searchResp = searchClient.search(searchRequest);
    //            searchRes = Optional.ofNullable(searchResp.getDatas()).map(SearchResponse::getDsmList)
    //                    .filter(productDTOS -> CollectionUtils.isNotEmpty(productDTOS)).orElse(new ArrayList<>());
    //            searchRes.stream().forEach(x -> {
    //                ProductCardDto e = ProductCardDto.buildBffSearchItem(x);
    //                list.add(e);
    //            });
    //            List<ProductCardDto> promptProducts = new ArrayList<>();
    //            list.forEach(x -> {
    //                ProductCardDto clone = BeanCopyUtil.copyPropertiesPlus(x, ProductCardDto::new);
    //                clone.setPrice(null);
    //                promptProducts.add(clone);
    //            });
    //            promptstringBuilder.append("#需要推荐的商品有:\n").append(promptProducts);
    //        }
    //        return list;
    //    }

    private ChatSessionContextBo getCacheChatContextBo(String session) throws IOException {
        String jsonCOntext = redisTemplate.opsForValue().get(RedisKeyConstants.CHAT_CONTEXT + session);
        if (StringUtils.isBlank(jsonCOntext)) {
            return new ChatSessionContextBo();
        }
        ChatSessionContextBo contextBo = JacksonConverterUtil.convertJsonToObject(jsonCOntext, ChatSessionContextBo.class);
        return contextBo;
    }

    private void setContext(ChatInputReqDto dto, ChatSessionContextBo contextBo) throws JsonProcessingException {
        redisTemplate.opsForValue().set(RedisKeyConstants.CHAT_CONTEXT + dto.getChatSessionId(), JacksonConverterUtil.convertObjectToJson(contextBo), Duration.ofMinutes(30));
    }


    private void sendVoice(Boolean enableSpeak, String msg) {
        if (enableSpeak) {
            String msgToVoice = new StringBuilder().append("<speak>").append(msg).append("</speak>").toString();
            CompletableFuture.runAsync(() -> {
                try {
                    digitalHumanService.sendMessage(msgToVoice);
                } catch (Exception e) {
                    log.error("send message error", e);
                }
            });
        }
    }


}
