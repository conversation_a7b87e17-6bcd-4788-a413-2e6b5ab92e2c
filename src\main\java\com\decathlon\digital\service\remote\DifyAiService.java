package com.decathlon.digital.service.remote;


import java.time.Duration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.decathlon.digital.common.constant.RedisKeyConstants;
import com.decathlon.digital.service.remote.dify.DifyChatRequest;
import com.decathlon.digital.service.remote.dify.DifyChatResponse;

import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.adapter.rxjava.RxJava2Adapter;

@Service
@Slf4j
@RequiredArgsConstructor
public class DifyAiService {

	protected final RedisTemplate<String, String> redisTemplate;
	protected final WebClient.Builder webClientBuilder;

	@Value("${ai.dify.api-key:app-cjqPZyM24Ttte2tPptZgx0cs}")
	protected String apiKey;
	@Value("${ai.dify.enable:true}")
	protected boolean aiEnable;
	@Value("${ai.dify.base-url:https://dify-api.dktapp.cloud/v1}")
	protected String baseUrl;

	public boolean enableAi() {
		return aiEnable;
	}

	protected void cacheSession(String webSession, String aiSession) {
		if (StringUtils.isBlank(aiSession) || StringUtils.isBlank(webSession)) {
			return;
		}
		String redisKey = RedisKeyConstants.appendRedisKey(RedisKeyConstants.SESSION_ID, webSession);
		redisTemplate.opsForValue().setIfAbsent(redisKey, aiSession, Duration.ofMinutes(30));
	}
	
    public String getSessionId(String webSession) {
        String redisKey = RedisKeyConstants.appendRedisKey(RedisKeyConstants.SESSION_ID, webSession);
        return redisTemplate.opsForValue().get(redisKey);
    }

	protected Flowable<DifyChatResponse> streamCallDifyAiByParam(DifyChatRequest param,String webSession) {
		log.info("call DifyAi streaming param {}", param);
		// Ensure response_mode is streaming for this call
		param.setInputs(null);
		param.setResponseMode("streaming");
		param.setFiles(null);
		
		Flux<DifyChatResponse> fluxResponse = webClientBuilder.baseUrl(baseUrl)
				.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
				.build()
				.post()
				.uri("/chat-messages")
				.contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.TEXT_EVENT_STREAM)
				.bodyValue(param)
				.retrieve()
				.bodyToFlux(DifyChatResponse.class)
				.doOnNext(data -> {
					log.info("Received Dify stream data: {}", data);
					if (StringUtils.isNotBlank(webSession) && StringUtils.isNotBlank(data.getConversationId())) {
						cacheSession(webSession, data.getConversationId());
					}
					log.info("Dify stream data: {}", data.getAnswer());
				})
				.doOnError(e -> log.error("Error during Dify streaming call: ", e))
				.onErrorResume(e -> {
					log.error("Dify streaming call failed: ", e);
					return Flux.empty();
				});

		return RxJava2Adapter.fluxToFlowable(fluxResponse);
	}
	
}
