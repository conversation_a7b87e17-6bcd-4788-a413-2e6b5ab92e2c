package com.decathlon.digital.common.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {

    /**
     * @deprecated Replaced by Seperator.COLON
     */
    @Deprecated
    public static final String COLON = Separator.COLON;

    public static final Integer DEFAULT_PAGE_NUMBER = 1;
    public static final Integer DEFAULT_PAGE_SIZE = 10;

    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAILURE = "failure";

    public static final long UNIT_MILLIS = TtlTime.TTL_1S_MILLIS;
    public static final long TIMEOUT_MILLIS = TtlTime.TTL_45S_MILLIS;

    public static final String EMPTY = StringUtils.EMPTY;
    public static final String DEFAULT_COUNTRY = "CN";

    public static final String REQUEST_ID_HEADER = "X-Correlation-Id";

    public static final String REQUEST_ID_LOG_KEY = "requestId";

    public static final String STOCKHUB_CHANNEL_ID = "1";

    public static final String CNDP_FILE_NAME = "order_easy_merch_daily/";
    public static final String ONLINE_SALES_FILE_NAME = "search_item_order_daily/";

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class MQ {

        public static final String CODE = "code";
        public static final String MSG = "msg";
        public static final String CONTENT = "content";
        public static final String SUCCESS = "0";
        public static final String BRAND = "brandCode";
        public static final String APP = "appCode";
        public static final String AGENT = "agentId";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Separator {

        public static final String COMMA = ",";
        public static final String SEMI_COLON = ";";
        public static final String COLON = ":";
        public static final String PLUS = "+";
        public static final String DASH = "-";
        public static final String STAR = "*";
        public static final String SLASH = "/";
        public static final String EQUAL = "=";
        public static final String PERCENT = "%";
        public static final String DOT = ".";
        public static final String UNDERSCORE = "_";
        public static final String PIPE = "|";
        public static final String QUOTE = "'";
        public static final String DOUBLE_QUOTE = "\"";
        public static final String AT = "@";
        public static final String QUESTION_MARK = "?";
        public static final String BRACE_LEFT = "(";
        public static final String BRACE_RIGHT = ")";
        public static final String BRACKET_LEFT = "[";
        public static final String BRACKET_RIGHT = "]";
        public static final String LINE_FEED = "\n";
        public static final String BLANK = " ";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class TtlTime {

        public static final long TTL_1S_MILLIS = 1000;
        public static final long TTL_45S_MILLIS = 45000;

        public static final long TTL_1_SEC = 1;
        public static final long TTL_3_SEC = 3;
        public static final long TTL_5_SEC = 5;
        public static final long TTL_10_SEC = 10;
        public static final long TTL_30_SEC = 30;
        public static final long TTL_45_SEC = 45;
        public static final long TTL_90_SEC = 90;

        public static final long TTL_1_MIN = 60;
        public static final long TTL_2_MIN = 120;
        public static final long TTL_5_MIN = 300;
        public static final long TTL_10_MIN = 600;
        public static final long TTL_15_MIN = 900;
        public static final long TTL_30_MIN = 1800;

        public static final long TTL_1_HOUR = 3600;
        public static final long TTL_2_HOUR = 7200;
        public static final long TTL_6_HOUR = 21600;
        public static final long TTL_12_HOUR = 43200;

        public static final long TTL_1_DAY = 86400;
        public static final long TTL_1_WEEK = 604800;
        public static final long TTL_1_MONTH = 2592000;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class DateTimePattern {

        public static final String GMT8_TIME_ZONE = "GMT+08:00";
        public static final String FMT_YEAR = "yyyy";
        public static final String FMT_YEAR_MONTH_SLIM = "yyyyMM";
        public static final String FMT_YEAR_MONTH = "yyyy-MM";
        public static final String FMT_BATCH_NUMBER = "yyyy.MM";
        public static final String FMT_DATE = "yyyy-MM-dd";
        public static final String FMT_DATE_SLASH = "yyyy/MM/dd";
        public static final String FMT_DATE_SLIM = "yyyyMMdd";
        public static final String FMT_PURE_DATE_SLIM = "MMdd";
        public static final String FMT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
        public static final String FMT_DATE_TIME_MILL = "yyyy-MM-dd HH:mm:ss.SSS";
        public static final String FMT_DATE_TIME_SLIM = "yyyyMMddHHmmss";
        public static final String FMT_DATE_TIME_LONG = "yyyyMMddHHmmssSSS";
        public static final String FMT_DATE_TIME_TZ = "yyyy-MM-dd'T'HH:mm:ss";
        public static final String FMT_DATE_TIME_TZ_JODA = "yyyy-MM-dd'T'HH:mm:ssZZ";
        public static final String FMT_DATE_TIME_TZ_IOS = "yyyy-MM-dd'T'HH:mm:ssXXX";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Store {

        public static final String STORE_NUMBER_ZERO_PREFIX = "0";
        public static final String CN_STORE_TYPE = "007";
        public static final int THIRD_TYPE_FULL_LENGTH = 3;
        public static final int THIRD_NUMBER_FULL_LENGTH = 5;
        public static final int LENGTH_STORE_NUMBER = 13;
        public static final int LENGTH_STORE_NUMBER_SPLIT = 3;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Protocol {

        public static final String HTTP = "http";
        public static final String HTTPS = "https";
        public static final String HTTP_PORT = "80";
        public static final String HTTPS_PORT = "443";
    }
}