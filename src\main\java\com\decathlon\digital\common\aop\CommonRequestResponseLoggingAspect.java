package com.decathlon.digital.common.aop;

import com.decathlon.digital.util.JacksonConverterUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Slf4j
@Component
public class CommonRequestResponseLoggingAspect {
    private final String LOG_FORMAT = "REQUEST DATA: [ {} {}{}, headers=[{}], payload={} ], RESPONSE DATA: [headers=[{}], body={} ]";

    @Pointcut("execution(* com.decathlon.digital.autherzation.controller.*.*(..))")
    public void logPointcut() {}

    @Around("logPointcut()")
    public Object logAround(ProceedingJoinPoint jp) throws Throwable {
        HttpServletRequest request = getRequest();
        String requestMethod = request.getMethod();
        String url = request.getRequestURI();

        // 请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        String paramStr = "";
        if (parameterMap != null && !parameterMap.isEmpty()) {
            StringBuilder paramSb = new StringBuilder("?");
            for (Map.Entry<String, String[]> param : parameterMap.entrySet()) {
                String[] value = param.getValue();
                paramSb.append(param.getKey())
                        .append("=")
                        .append(value != null && value.length > 0 ? value[0] : "")
                        .append("&");
            }
            paramStr = paramSb.substring(0, paramSb.length() - 1);
        }

        // 请求头
        String requestHeaders = formatRequestHeaders(request);

        // 请求体
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Object requestBody = null;
        Method method = signature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        Object[] args = jp.getArgs();
        for (int i = 0; i < parameterAnnotations.length; i++) {
            for (int j = 0; j < parameterAnnotations[i].length; j++) {
                if (parameterAnnotations[i][j] instanceof RequestBody) {
                    requestBody = args[i];
                    break;
                }
            }
        }

        // 响应体
        final Object responseBody = jp.proceed();

        // 响应头
        HttpServletResponse response = getResponse();
        String responseHeaders = formatResponseHeaders(response);

        // 统一日志输出
        String requestBodyJson = JacksonConverterUtil.convertObjectToJson(requestBody);
        String responseBodyJson = JacksonConverterUtil.convertObjectToJson(requestBody);
        log.debug("REQUEST DATA: [ {} {}{}, headers=[{}], payload={} ], RESPONSE DATA: [headers=[{}], body={} ]",
                requestMethod, url, paramStr, requestHeaders, requestBodyJson, responseHeaders, responseBodyJson);

        return responseBody;
    }


    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Objects.requireNonNull(attributes).getRequest();
    }

    private HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Objects.requireNonNull(attributes).getResponse();
    }

    private String formatRequestHeaders(HttpServletRequest request) throws JsonProcessingException {
        Enumeration<String> enumeration = request.getHeaderNames();
        Map<String, String> requestHeaders = new HashMap<>();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            requestHeaders.put(key, request.getHeader(key));
        }
        return JacksonConverterUtil.convertObjectToJson(requestHeaders);
    }

    private String formatResponseHeaders(HttpServletResponse response) throws JsonProcessingException {
        Map<String, String> responseHeaders = new HashMap<>();
        for (String headerName : response.getHeaderNames()) {
            responseHeaders.put(headerName, response.getHeader(headerName));
        }
        return JacksonConverterUtil.convertObjectToJson(responseHeaders);
    }
}
