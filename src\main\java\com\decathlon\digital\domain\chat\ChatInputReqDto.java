package com.decathlon.digital.domain.chat;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @auther <PERSON>
 * @since 2025-03-07
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatInputReqDto {
    @NotBlank
    protected String content;

    @NotNull
    protected Long chatSessionId;

    //大模型记忆的上下文长度
    protected Integer contextLength =5;



}
