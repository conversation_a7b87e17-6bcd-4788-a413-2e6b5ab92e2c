package com.decathlon.digital.service.remote.dify;


import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyChatResponse {

    private String event;
    @SerializedName("task_id")
    private String taskId;
    private String id;
    @SerializedName("message_id")
    private String messageId;
    @SerializedName("conversation_id")
    private String conversationId;
    private Metadata metadata;
    @SerializedName("created_at")
    private Long createdAt;
    private String files;
    private String answer;


} 