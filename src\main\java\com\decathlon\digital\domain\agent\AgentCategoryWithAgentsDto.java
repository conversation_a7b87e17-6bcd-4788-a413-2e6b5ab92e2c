package com.decathlon.digital.domain.agent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分类及其下属Agent的响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentCategoryWithAgentsDto {

    /**
     * 分类名称
     */
    private String categoryName;
    
    private String categoryIconName;
    
    /**
     * 该分类下的Agent列表
     */
    private List<AgentSimpleDto> agents;
}
