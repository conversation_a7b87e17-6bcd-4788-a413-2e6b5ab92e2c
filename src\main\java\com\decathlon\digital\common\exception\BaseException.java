package com.decathlon.digital.common.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @create 2021-04-01
 */
@Getter
@Setter
public abstract class BaseException extends RuntimeException {

    protected String code;
    protected Object data;

    @Override
    public String toString() {
        String string = super.toString();
        return new StringBuilder(string).append(", code=").append(code).append(", data=").append(data).toString();
    }

    public BaseException(BaseError baseError) {
        super(baseError.getMessage());
        this.code = baseError.getCode();
    }

    public BaseException(BaseError baseError, Object... params) {
        super(baseError.withParams(params));
        this.code = baseError.getCode();
    }


    protected BaseException(String code, String message, Throwable cause) {
        super(message);
        this.code = code;
        this.data = null;
    }


    public BaseException(String message) {
        super(message);
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }


    public HttpStatus getHttpStatus() {
        return HttpStatus.INTERNAL_SERVER_ERROR;
    }

}
