package com.decathlon.digital.common.dto;

import com.decathlon.digital.common.exception.BaseError;
import lombok.Data;

@Data
public class BaseApiResponseDto<T> {

    private static final String SUCCESS_CODE = "0";
    private static final String SUCCESS_MSG = "success";

    protected String code;
    protected String message;
    protected T data;

    public BaseApiResponseDto() {
    }

    public BaseApiResponseDto(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static BaseApiResponseDto buildFromError(BaseError error) {
        return new BaseApiResponseDto(error.getCode(), error.getMessage(), null);
    }

    public static BaseApiResponseDto buildFromError(BaseError error, String... params) {
        return new BaseApiResponseDto(error.getCode(), error.withParams(params), null);
    }

    public static BaseApiResponseDto fail(String codeMessage, String errorMessage) {
        return new BaseApiResponseDto(codeMessage, errorMessage, null);
    }

    public static BaseApiResponseDto fail(String codeMessage, String code, String errorMessage) {
        String s = code + "; " + errorMessage;
        return new BaseApiResponseDto(codeMessage, s, null);
    }

    public static <T> BaseApiResponseDto<T> ok(T data) {
        return new BaseApiResponseDto<>(SUCCESS_CODE, SUCCESS_MSG, data);
    }

}