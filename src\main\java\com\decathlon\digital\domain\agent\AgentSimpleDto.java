package com.decathlon.digital.domain.agent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Agent简化信息DTO
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AgentSimpleDto {

    /**
     * Agent名称
     */
    private String name;
    
    /**
     * Agent代码
     */
    private String agentCode;
    
    /**
     * 图标名称
     */
    private String iconName;
    
    /**
     * 卡片背景URL
     */
    private String cardBgUrl;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 是否激活
     */
    private Boolean active;
    
    
    private String label;
}
