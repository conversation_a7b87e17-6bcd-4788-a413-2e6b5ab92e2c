package com.decathlon.digital.domain.conversation.entity;

import com.decathlon.digital.domain.BaseTrasactionEntity;
import com.decathlon.digital.domain.conversation.ConversationStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.ai.chat.messages.MessageType;

import java.util.Map;

/**
 * @auther Shi Lei
 * @since 2025-05-14
 */
@Data
@Entity
@Table(name = "chat_conversation", schema = "ai_agent")
public class ChatConversation extends BaseTrasactionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "TEXT")
    private String content;

    @Enumerated(jakarta.persistence.EnumType.STRING)
    private MessageType messageType;


    private Long chatSessionId;
    @Enumerated(jakarta.persistence.EnumType.STRING)
    private ConversationStatus status ;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private Map<String,Object> metadata;

}
