package com.decathlon.digital.service.remote.dify;

import java.util.List;
import java.util.Map;

import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Metadata {
    // Depending on the actual metadata structure, you might need to add more fields here.
    // For now, let's keep it simple or as a generic map if its content varies greatly.
    private Map<String, Object> callbacks;
    @SerializedName("retriever_resources")
    private List<RetrieverResource> retrieverResources;
    private Usage usage;
}