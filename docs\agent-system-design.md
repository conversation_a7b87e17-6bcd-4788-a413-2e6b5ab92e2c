# AI Agent配置系统设计文档

## 1. 概述

本文档描述了AI Agent配置系统的重构设计，将原有的硬编码Agent配置改为基于数据库的动态配置系统，支持分类管理、状态控制、图标配置等扩展功能。

## 2. 系统架构

### 2.1 核心组件

- **数据库层**: PostgreSQL数据库存储Agent和分类配置
- **实体层**: JPA实体类映射数据库表
- **Repository层**: 数据访问接口
- **服务层**: 业务逻辑处理
- **控制器层**: REST API接口
- **缓存层**: 提升查询性能（可选）

### 2.2 数据模型

#### Agent分类表 (agent_category)
```sql
CREATE TABLE ai_agent.agent_category (
    id BIGSERIAL PRIMARY KEY,
    category_code VARCHAR(50) NOT NULL UNIQUE,
    category_name VARCHAR(100) NOT NULL,
    category_icon VARCHAR(255),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    create_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### AI Agent表 (ai_agent)
```sql
CREATE TABLE ai_agent.ai_agent (
    id BIGSERIAL PRIMARY KEY,
    agent_code VARCHAR(50) NOT NULL UNIQUE,
    agent_name VARCHAR(100) NOT NULL,
    agent_type VARCHAR(25) NOT NULL,
    category_id BIGINT NOT NULL,
    agent_icon VARCHAR(255),
    description TEXT,
    remote_app_id VARCHAR(255),
    ai_service_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_agent_category FOREIGN KEY (category_id) REFERENCES ai_agent.agent_category(id)
);
```

## 3. 核心功能

### 3.1 Agent分类管理
- 支持多级分类（当前为一级分类）
- 分类图标配置
- 分类排序
- 分类状态控制

### 3.2 Agent配置管理
- Agent基本信息配置
- 图标和描述配置
- 所属分类关联
- 可用状态控制
- 默认显示设置
- 排序配置

### 3.3 动态加载
- 从数据库动态加载Agent配置
- 支持运行时配置更新
- 缓存机制提升性能

## 4. API接口

### 4.1 Agent相关接口

#### 获取所有Agent列表
```
GET /api/v1/agents
```

#### 获取默认显示的Agent列表
```
GET /api/v1/agents/default
```

#### 根据分类获取Agent列表
```
GET /api/v1/agents/category/{categoryId}
```

#### 流式调用Agent
```
POST /api/v1/agents/stream
```

### 4.2 分类相关接口

#### 获取所有分类列表
```
GET /api/v1/agents/categories
```

## 5. 数据字段说明

### 5.1 Agent字段
- `agent_code`: Agent唯一标识码
- `agent_name`: Agent显示名称
- `agent_type`: Agent类型（TASK/CHAT/CREATE）
- `category_id`: 所属分类ID
- `agent_icon`: Agent图标路径
- `description`: Agent描述信息
- `remote_app_id`: 远程应用ID
- `ai_service_type`: AI服务类型（QWEN/DIFY等）
- `is_active`: 是否可用
- `is_default`: 是否默认显示
- `sort_order`: 排序顺序

### 5.2 分类字段
- `category_code`: 分类唯一标识码
- `category_name`: 分类显示名称
- `category_icon`: 分类图标路径
- `description`: 分类描述信息
- `sort_order`: 排序顺序
- `is_active`: 是否激活

## 6. 使用说明

### 6.1 添加新Agent
1. 在`agent_category`表中确保存在对应分类
2. 在`ai_agent`表中插入新Agent记录
3. 确保`ai_service_type`字段值在系统中有对应的服务实现

### 6.2 修改Agent配置
1. 直接更新数据库中的Agent记录
2. 系统会自动加载最新配置

### 6.3 分类管理
1. 添加新分类：在`agent_category`表中插入记录
2. 修改分类：更新对应记录
3. 禁用分类：设置`is_active`为false

## 7. 扩展功能

### 7.1 缓存机制
- 支持Redis缓存Agent配置
- 提升查询性能
- 支持缓存失效和刷新

### 7.2 配置热更新
- 支持运行时配置更新
- 无需重启应用

### 7.3 权限控制
- 可扩展基于角色的Agent访问控制
- 支持用户级别的Agent可见性配置

## 8. 注意事项

1. **数据一致性**: 确保Agent的`ai_service_type`在系统中有对应实现
2. **性能优化**: 对于高频查询，建议启用缓存机制
3. **数据迁移**: 从硬编码配置迁移到数据库配置时，需要执行数据迁移脚本
4. **图标资源**: 确保图标文件存在于指定路径
5. **排序规则**: 按`sort_order`升序，相同时按`id`升序

## 9. 部署步骤

1. 执行数据库迁移脚本：`V1.0.3__create_agent_tables.sql`
2. 验证默认数据是否正确插入
3. 重启应用服务
4. 测试API接口功能
5. 验证Agent调用功能正常

## 10. 故障排查

### 10.1 常见问题
- Agent不显示：检查`is_active`字段
- Agent调用失败：检查`ai_service_type`和`remote_app_id`配置
- 分类不显示：检查分类的`is_active`字段
- 排序异常：检查`sort_order`字段值

### 10.2 日志查看
- 查看应用启动日志确认AI服务映射初始化
- 查看数据库查询日志确认SQL执行情况
