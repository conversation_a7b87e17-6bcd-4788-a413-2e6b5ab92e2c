package com.decathlon.digital.service.remote;


import javax.net.ssl.SSLException;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.decathlon.digital.App;
import com.decathlon.digital.service.remote.dify.DifyChatRequest;

import lombok.extern.slf4j.Slf4j;


@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
class DifyAiServiceTest {
	
	@Autowired
	DifyAiService difyAiService;

	@Test
	void testStreamCallDifyAiByParam() throws SSLException {
		
		DifyChatRequest param = new DifyChatRequest();
		param.setQuery("Hello, Dify AI!");
		
		difyAiService.streamCallDifyAiByParam(param,"222").subscribe(
			response -> log.info("Received response: {}", response),
			error -> log.error("Error occurred: {}", error.getMessage()),
			() -> log.info("Stream completed")
		);
	}

}
