//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.decathlon.digital.common.exception;

public class ServiceException extends BaseException {
    private static final long serialVersionUID = 4058317721550486338L;
    private static final String GENERIC_SERVICE_ERROR = "Internal Service Exception";



    public ServiceException(BaseError error, Object... params) {
        super(error, params);
    }

//    public ServiceException(String code, String message, Throwable e, Object data) {
//        super(code, message, e, data);
//    }
//
//    public ServiceException(Object data, BaseError error, Object... params) {
//        super(data, error, params);
//    }
}
