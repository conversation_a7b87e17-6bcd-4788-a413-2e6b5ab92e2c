package com.decathlon.digital.common.exception;


public enum SystemError implements BaseError {
    INPUT_CONVERT_ERROR("err_err_system_1", "Input parameter cannot be converted."),
    ACCESS_DENIED("err_system_2", "Access Denied."),
    NOT_FOUND("err_system_3", "[{0}] not exists with [{1}] . "),
    SYSTEM_INTERNAL_ERROR("err_system_4", "err_system internal error."),
    INVALID_PARAM_ERROR("err_system_5", "[{0}] {1}"),
    REMOTE_REST_ERROR("err_system_6", "Call remote server {0} error."),
    FILE_UPLOAD_ERROR("err_system_7", "File {0} upload error."),
    FILE_DOWNLOAD_ERROR("err_system_8", "File {0} download error."),
    DATASOURCE_ROUTING_ERROR("err_system_9", "Method {0} is not allowed to use master datasource."),
    JSON_SERIALIZE_ERROR("err_system_10", "Parse an object to json string error."),
    JSON_DESERIALIZE_ERROR("err_system_11", "Parse json string to object error."),
    UNAUTHORIZED("err_system_12", "Unauthorized"),
    ;


    private final String code;
    private final String message;

    SystemError(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
