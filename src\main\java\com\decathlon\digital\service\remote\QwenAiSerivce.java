package com.decathlon.digital.service.remote;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationOutput;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.decathlon.digital.common.constant.RedisKeyConstants;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.reactivex.Flowable;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * @auther Shi Lei
 * @since 2024-10-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QwenAiSerivce {

    protected final RedisTemplate<String, String> redisTemplate;

    @Value("${ai.qwen.api-key}")
    protected String apiKey;
    @Value("${ai.qwen.enable}")
    protected boolean aiEnable;

    @Value("${ai.proxy.host}")
    protected String proxyHost;
    @Value("${ai.proxy.port}")
    protected String proxyPort;

    private static final String DEFAULT_VALUE = "";

    @PostConstruct
    public void setProxy() {
        if (StringUtils.isNotBlank(proxyHost) && StringUtils.isNotBlank(proxyPort)) {
            log.info("set qwen proxy {} {}", proxyHost, proxyPort);
            System.setProperty("DASHSCOPE_PROXY_HOST", proxyHost);
            System.setProperty("DASHSCOPE_PROXY_PORT", proxyPort);
        }
    }


    public boolean enableAi() {
        return aiEnable;
    }


    public String concatPrompt(String currentPrompt, String lastValue) {
        if (StringUtils.isNotBlank(lastValue)) {
            return new StringBuilder().append("当前需要处理的数据为:").append(currentPrompt).append(";").append(System.lineSeparator())
                    .append("上次清洗的结果为:").append(lastValue).toString();
        } else {
            return currentPrompt;
        }
    }

    public String callqwenAi(String body, String appid) {
        return callqwenAi(body, appid, false);
    }

    public String callqwenAi(String body, String appid, boolean forceExcute) {
        if (!forceExcute) {
            if (!enableAi()) {
                return DEFAULT_VALUE;
            }
        }
        ApplicationParam param = buildApplicationParam(body, appid);
        return callQwenAiByParam(param);

    }


    public String callAiWithImage(String prompt, String appid, String imageUrl) {
        if (!enableAi()) {
            return DEFAULT_VALUE;
        }
        ApplicationParam param = buildApplicationParam(prompt, appid);
        param.setImages(List.of(imageUrl));
        return callQwenAiByParam(param);
    }


    public String chatWithQwenAi(String prompt, String appid, String webSession, Map<String, Object> bizParams) {
        ApplicationParam param = buildApplicationParam(prompt, appid);
        String sessionId = getSessionId(webSession);
        param.setSessionId(sessionId);
        if (bizParams != null) {
            setParam(bizParams, param);
        }
        return callQwenAiByParam(webSession, param);
    }


    protected static void setParam(Map<String, Object> bizParams, ApplicationParam param) {
        Gson gson = new Gson();
        JsonObject asJsonObject = gson.toJsonTree(bizParams).getAsJsonObject();
        param.setBizParams(asJsonObject);
    }

    protected void cacheSession(String webSession, String aiSession) {
        if (StringUtils.isBlank(aiSession) || StringUtils.isBlank(webSession)) {
            return;
        }
        String redisKey = RedisKeyConstants.appendRedisKey(RedisKeyConstants.SESSION_ID, webSession);
        redisTemplate.opsForValue().setIfAbsent(redisKey, aiSession, Duration.ofMinutes(30));
    }

    public String getSessionId(String webSession) {
        String redisKey = RedisKeyConstants.appendRedisKey(RedisKeyConstants.SESSION_ID, webSession);
        return redisTemplate.opsForValue().get(redisKey);
    }


    protected String callQwenAiByParam(ApplicationParam param) {
        return callQwenAiByParam(null, param);
    }

    protected String callQwenAiByParam(String webSession, ApplicationParam param) {
        Application application = new Application();
        ApplicationResult result = null;
        try {
            result = application.call(param);
            ApplicationOutput output = result.getOutput();
            String resultText = output.getText();
            if (StringUtils.isNotBlank(webSession)) {
                cacheSession(webSession, output.getSessionId());
            }
            log.info("call qwenAi success {} result {} ", result.getRequestId(), resultText);
            return AiRespFormat.handleDifferentRespFormat(resultText);
        } catch (Exception e) {
            log.error("call qwenAi error ", e);
            return DEFAULT_VALUE;
        }
    }

    protected Flowable<ApplicationResult> streamCallQwenAiByParam(String webSession, ApplicationParam param) {
        Application application = new Application();
        try {
            log.info("call qwenAi param {}", param);
            Flowable<ApplicationResult> result = application.streamCall(param);

            result.forEach(data -> {
                ApplicationOutput dataOutput = data.getOutput();
                log.info("call qwenAi success {} result {} ", data.getRequestId(), dataOutput);
                cacheSession(webSession, dataOutput.getSessionId());
            });
            return result;
        } catch (
                Exception e) {
            log.error("call qwenAi error ", e);
            return Flowable.empty();
        }
    }


    protected ApplicationParam buildApplicationParam(String body, String appid) {
        ApplicationParam param = ApplicationParam.builder()
                .apiKey(apiKey)
                .appId(appid)
                .prompt(body)
                .build();
        return param;
    }

}
