package com.decathlon.digital.service.remote.dify;

import com.google.gson.annotations.SerializedName;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrieverResource {
	@SerializedName("dataset_id")
    private String datasetId;
    @SerializedName("dataset_name")
    private String datasetName;
    @SerializedName("document_id")
    private String documentId;
    @SerializedName("document_name")
    private String documentName;
    @SerializedName("data_source_type")
    private String dataSourceType;
    @SerializedName("segment_id")
    private String segmentId;
    @SerializedName("retriever_from")
    private String retrieverFrom;
    private Double socre;
    /**
     * contain metadata of the document, such as title, author, date, etc.
     */
    @SerializedName("doc_metadata")
    private JSONObject docMetadata;
    private String content;
    private Integer position;

}