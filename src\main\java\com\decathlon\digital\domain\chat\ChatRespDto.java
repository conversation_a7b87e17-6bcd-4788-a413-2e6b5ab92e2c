package com.decathlon.digital.domain.chat;

import com.decathlon.digital.domain.conversation.dto.ConversationPairDto;
import com.decathlon.digital.service.remote.dify.Metadata;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @auther <PERSON>
 * @since 2025-03-11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatRespDto {

    protected String content;
    
    protected String event;

    protected String finishReason;
    
    private Metadata metadata;
    
    protected ConversationPairDto conversationPair;


}
