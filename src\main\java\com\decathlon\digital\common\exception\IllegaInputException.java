package com.decathlon.digital.common.exception;


import org.springframework.http.HttpStatus;

public class IllegaInputException extends BaseException {


  public IllegaInputException(BaseError detail) {
    super(detail);
  }

  public IllegaInputException(BaseError baseError, Object... params) {
    super(baseError, params);
  }

  public IllegaInputException(String message) {
    super(message);
  }

  public IllegaInputException(String message, Throwable cause) {
    super(message, cause);
  }

  @Override
  public HttpStatus getHttpStatus() {
    return HttpStatus.BAD_REQUEST;
  }


}
