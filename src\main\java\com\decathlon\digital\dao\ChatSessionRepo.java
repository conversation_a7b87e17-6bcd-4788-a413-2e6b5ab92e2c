package com.decathlon.digital.dao;

import com.decathlon.digital.domain.conversation.entity.ChatSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * @auther <PERSON>
 * @since 2025-05-16
 */
@Repository
public interface ChatSessionRepo extends JpaRepository<ChatSession, Long>, JpaSpecificationExecutor<ChatSession> {
    @Query("select c from ChatSession c where c.sessionId = ?1 and c.profile = ?2 and c.isDeleted = false")
    ChatSession findBySessionIdAndProfile(String sessionId, String profile);

    @Query("select c from ChatSession c where c.profile = ?1 and c.isDeleted = false")
    Page<ChatSession> findByProfile(String profile, Pageable pageable);

    @Query("update ChatSession c set c.isDeleted = true, c.updateTime = current_timestamp where id = ?1")
    @Modifying
    void updateAsDeleted(Long sessionId);

    @Query("select c from ChatSession c where c.id = ?1 and c.isDeleted = false")
    Optional<ChatSession> findByIdAndNotDeleted(Long sessionId);
}
