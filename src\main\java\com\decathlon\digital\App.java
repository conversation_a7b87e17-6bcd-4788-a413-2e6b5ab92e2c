package com.decathlon.digital;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.TimeZone;


@SpringBootApplication
@Slf4j
@EnableAsync
public class App {

    @Value("${server.timeZone:#{T(java.time.ZoneId).systemDefault().getId()}}")
    private String timeZone;


    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }

    @PostConstruct
    void setDefaultTimeZone() {
        TimeZone.setDefault(TimeZone.getTimeZone(timeZone));
        log.info(
                "timeZone :" + timeZone + " Date: " + new Date() + "local date:" + LocalDateTime.now());
    }


}
