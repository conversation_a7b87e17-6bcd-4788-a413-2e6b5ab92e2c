package com.decathlon.digital.service.remote.dify;

import java.util.List;
import java.util.Map;

import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyChatRequest {
    private String query;
    private Map<String, Object> inputs;
    @SerializedName("response_mode")
    private String responseMode;
    private String user;
    @SerializedName("conversation_id")
    private String conversationId;
    private List<DifyFile> files;
    @SerializedName("auto_generate_name")
    private Boolean autoGenerateName;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyFile {
        private String type;
        @SerializedName("transfer_method")
        private String transferMethod;
        private String url;
        @SerializedName("upload_file_id")
        private String uploadFileId;
    }
} 