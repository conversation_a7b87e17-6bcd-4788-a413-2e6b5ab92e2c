package com.decathlon.digital.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.function.BiFunction;
import java.util.function.Consumer;

/**
 * @auther Shi Lei
 * @since 2024-12-31
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChunkHelper<T> {

    private final Executor taskExecutor;

    public <T> void runWithChuck(List<T> list, int chunkSize, Consumer<? super List<T>> consumer) {
        int taskCount = list.size() / chunkSize;
        if (list.size() % chunkSize != 0) {
            taskCount++;
        }
        log.info("runWithChuck thread count {}", taskCount);
        // 创建一个CountDownLatch，计数器初始化为任务的数量
        CountDownLatch latch = new CountDownLatch(taskCount);

        for (int i = 0; i < list.size(); i += chunkSize) {
            // 计算子List的结束索引，确保不会超出原始List的范围
            int end = Math.min(i + chunkSize, list.size());

            // 获取子List
            List<T> chunkList = list.subList(i, end);

            CompletableFuture.runAsync(() -> consumer.accept(chunkList), taskExecutor)
                    .whenComplete((unused, throwable) -> latch.countDown());

        }//end for

        try {
            latch.await();
            log.info("runWithChuck finish");
        } catch (InterruptedException e) {
            log.error("runWithChuck fail", e);
            Thread.currentThread().interrupt();
        }
    }


    public <E> void runWithPage(int pageSize, BiFunction<Integer, Integer, ? extends List<E>> pageQueryFunction, Consumer<List<E>> consumer) {
        int pageNum = 1;
        List<E> pageList = new ArrayList<>();
        do {
            pageList = pageQueryFunction.apply(pageNum, pageSize);
            consumer.accept(pageList);
        } while (pageList.size() >= pageSize);
    }

}
