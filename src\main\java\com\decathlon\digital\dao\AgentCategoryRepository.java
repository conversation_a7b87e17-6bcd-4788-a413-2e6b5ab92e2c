package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.entity.AgentCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Agent分类Repository接口
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface AgentCategoryRepository extends JpaRepository<AgentCategory, Long> {
    
    /**
     * 查找所有激活的分类，按排序顺序排列
     * 
     * @return 激活的分类列表
     */
    @Query("SELECT c FROM AgentCategory c WHERE c.isActive = true ORDER BY c.sortOrder ASC, c.id ASC")
    List<AgentCategory> findAllActiveOrderBySortOrder();
    
}
