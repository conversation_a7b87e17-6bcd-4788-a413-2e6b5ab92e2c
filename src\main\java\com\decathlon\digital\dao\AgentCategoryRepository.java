package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.entity.AgentCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Agent分类Repository接口
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Repository
public interface AgentCategoryRepository extends JpaRepository<AgentCategory, Long> {
    
    /**
     * 根据分类代码查找分类
     * 
     * @param categoryCode 分类代码
     * @return 分类信息
     */
    Optional<AgentCategory> findByCategoryCode(String categoryCode);
    
    /**
     * 查找所有激活的分类，按排序顺序排列
     * 
     * @return 激活的分类列表
     */
    @Query("SELECT c FROM AgentCategory c WHERE c.isActive = true ORDER BY c.sortOrder ASC, c.id ASC")
    List<AgentCategory> findAllActiveOrderBySortOrder();
    
    /**
     * 根据激活状态查找分类
     * 
     * @param isActive 是否激活
     * @return 分类列表
     */
    List<AgentCategory> findByIsActiveOrderBySortOrderAsc(Boolean isActive);
    
    /**
     * 检查分类代码是否存在
     * 
     * @param categoryCode 分类代码
     * @return 是否存在
     */
    boolean existsByCategoryCode(String categoryCode);
}
