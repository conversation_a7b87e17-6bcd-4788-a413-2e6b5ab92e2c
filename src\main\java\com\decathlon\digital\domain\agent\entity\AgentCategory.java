package com.decathlon.digital.domain.agent.entity;

import com.decathlon.digital.domain.BaseTimeEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Agent分类实体类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Table(name = "agent_category")
public class AgentCategory extends BaseTimeEntity {
    
    private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 分类代码，唯一标识
     */
    @Column(name = "category_code", nullable = false, unique = true, length = 50)
    private String categoryCode;
    
    /**
     * 分类名称
     */
    @Column(name = "category_name", nullable = false, length = 100)
    private String categoryName;
    
    /**
     * 分类图标路径
     */
    @Column(name = "category_icon_name")
    private String categoryIconName;
    
    /**
     * 分类描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;
    
    /**
     * 是否激活
     */
    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;
    
    /**
     * 该分类下的Agent列表
     */
    @OneToMany(mappedBy = "category")
    private List<AiAgent> agents;
}
