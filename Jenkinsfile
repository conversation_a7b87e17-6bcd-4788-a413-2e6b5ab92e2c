#!/usr/bin/env groovy
// @Library('custom-lib@feat_cn_methods') _
@Library('custom-lib@feat_cn_methods_k8s') _

/*

Arguments need to be modified:

- stack_name                         # stack name that the service is located in rancher, e.g. facade-services
- image_name                         # path to your image in registry, e.g. facade/payment-api
- service_name                       # service name in Rancher, same to the souce code repository name by default
- rancher_access_key_credential_id   # credential id of rancher access key
- rancher_secret_key_credential_id   # credential id of rancher secret key
- email_to                           # email receivers, e.g. shiphub@decathlon.<NAME_EMAIL>
- dockerfile_path                    # path to the Dockerfile, '.' by default
- jdk                                # jdk version e.g. java11 java12 java8
- maven                              # maven version e.g. maven3.6 maven 3.5
- daas_job_id                        # DaaS job id associated to this project

*/

def args = [:]

args.image_name = 'ai-chatbox/ai-agent'

if (env.BRANCH_NAME == 'main') {
    args.targetEnv = 'production'
    args.releaseType= 'RELEASE'
} else {
    args.targetEnv = 'dev'
    args.releaseType= 'SNAPSHOT'
}

args.email_to   = '<EMAIL>'

args.dockerfile_path = '.'

args.jdk = 'java17'
args.maven = 'maven3.9.6'


pipeline_java(args)
