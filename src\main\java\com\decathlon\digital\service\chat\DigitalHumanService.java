package com.decathlon.digital.service.chat;

import com.aliyun.avatar20220130.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.decathlon.digital.common.constant.RedisKeyConstants;
import com.decathlon.digital.util.JacksonConverterUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * @auther Shi Lei
 * @since 2025-03-11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigitalHumanService {


    @Value("${ai.digitalHuman.accessKeyId}")
    private String accessKeyId;

    @Value("${ai.digitalHuman.accessKeySecret}")
    private String accessKeySecret;

    @Value("${ai.digitalHuman.endpoint}")
    private String endpoint;

    @Value("${ai.digitalHuman.appId}")
    private String appId;

    @Value("${ai.digitalHuman.backGroundImageUrl}")
    private String backGroundImageUrl;

    @Value("${ai.digitalHuman.tenantId}")
    private Long tenantId;

    @Value("${ai.proxy.host}")
    private String proxyHost;
    @Value("${ai.proxy.port}")
    private String proxyPort;

    private static com.aliyun.avatar20220130.Client client;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private com.aliyun.avatar20220130.Client createClient() throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                //.setAccessKeyId("LTAI5tLdbmwjSnudkvka7V9E")
                .setAccessKeyId(accessKeyId)
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
//                .setAccessKeySecret("******************************");
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/avatar
//        config.endpoint = "avatar.cn-zhangjiakou.aliyuncs.com";
        config.endpoint = endpoint;
        config.httpProxy = "https://"+proxyHost+":"+proxyPort;
        //config.httpProxy = "https://proxy-internet-ali-cnsh.pp.dktapp.cloud:3128";
        return client = new com.aliyun.avatar20220130.Client(config);
    }


    public StartInstanceResponseBody startInstance() throws Exception {

        StartInstanceRequest startInstanceRequest = buildStartInstanceRequest();
        RuntimeOptions runtime = buildRuntimeOptions();

        try {
            // 复制代码运行请自行打印 API 的返回值
            StartInstanceResponse startInstanceResponse = getClient().startInstanceWithOptions(startInstanceRequest, runtime);
            log.info("startInstanceWithOptions resp: {}", JacksonConverterUtil.convertObjectToJson(startInstanceResponse));
            StartInstanceResponseBody body = startInstanceResponse.getBody();
            if (body.success){
                redisTemplate.opsForValue().set(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID, body.data.sessionId);
            }
            return body;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error(error.getData().get("Recommend").toString());
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error(error.getMessage());
            // 诊断地址
            log.error(error.getData().get("Recommend").toString());
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    private RuntimeOptions buildRuntimeOptions() {
        return new RuntimeOptions();
    }

    private StartInstanceRequest buildStartInstanceRequest() {

        StartInstanceRequest.StartInstanceRequestCommandRequest commandRequest = new StartInstanceRequest.StartInstanceRequestCommandRequest();
        commandRequest.setLocate(0);
        commandRequest.setAlphaSwitch(false);
        commandRequest.setBackGroundImageUrl(backGroundImageUrl);
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))

        StartInstanceRequest.StartInstanceRequestUser user = new StartInstanceRequest.StartInstanceRequestUser();
        user.setUserId("verowaves");
        user.setUserName("Vero");


        StartInstanceRequest.StartInstanceRequestApp app = new StartInstanceRequest.StartInstanceRequestApp();
        app.setAppId(appId);


        StartInstanceRequest startInstanceRequest = new StartInstanceRequest();
        startInstanceRequest.setTenantId(tenantId);
        startInstanceRequest.setApp(app);
        startInstanceRequest.setUser(user);
        startInstanceRequest.setCommandRequest(commandRequest);
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                //.build();

        return startInstanceRequest;
    }


    public Boolean stopInstance() throws Exception {

        String sessionId = redisTemplate.opsForValue().get(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID);
        StopInstanceRequest stopInstanceRequest = new StopInstanceRequest()
                .setTenantId(tenantId)
                .setSessionId(sessionId);

        try{
            StopInstanceResponse stopInstanceResponse = getClient().stopInstance(stopInstanceRequest);
            log.info("stopInstance resp: {}", JacksonConverterUtil.convertObjectToJson(stopInstanceResponse));
            return stopInstanceResponse.getBody().success;
        }catch (Exception exception){
            log.error("stopInstance error", exception);
        }
        return Boolean.FALSE;
    }


    public List<QueryRunningInstanceResponseBody.QueryRunningInstanceResponseBodyData> queryRunningInstance() throws Exception {

        // Parameter settings for API request
        QueryRunningInstanceRequest.QueryRunningInstanceRequestApp app = new QueryRunningInstanceRequest.QueryRunningInstanceRequestApp()
                .setAppId(appId);

        String sessionId = redisTemplate.opsForValue().get(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID);

        QueryRunningInstanceRequest queryRunningInstanceRequest = new QueryRunningInstanceRequest()
        .setTenantId(tenantId)
        .setApp(app)
        .setSessionId(StringUtils.isEmpty(sessionId) ? null : sessionId);


        try{
            QueryRunningInstanceResponse response = getClient().queryRunningInstance(queryRunningInstanceRequest);
            log.info("queryRunningInstance resp: {}", JacksonConverterUtil.convertObjectToJson(response));
            QueryRunningInstanceResponseBody body = response.getBody();
            if (body.success){
                redisTemplate.opsForValue().set(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID, body.getData().get(0).getSessionId());
            }
            return body.getData();
        }catch (Exception e){
            log.error("queryRunningInstance error", e);
        }
        return Lists.newArrayList();
    }


    public Boolean sendMessage(String text)throws Exception {

        String sessionId = redisTemplate.opsForValue().get(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID);


        SendMessageRequest.SendMessageRequestTextRequest textRequest = new SendMessageRequest.SendMessageRequestTextRequest()
                .setCommandType("START")
                .setSpeechText(text)
                .setId(UUID.randomUUID().toString())
                .setInterrupt(true);
        SendMessageRequest sendMessageRequest = new SendMessageRequest()
                .setSessionId(sessionId)
                .setTenantId(tenantId)
                .setTextRequest(textRequest);

        SendMessageResponse response = getClient().sendMessage(sendMessageRequest);
        log.info("sendMessage resp: {}" , JacksonConverterUtil.convertObjectToJson(response));
        return response.getBody().success;
    }


    private com.aliyun.avatar20220130.Client getClient()throws Exception {
        if (Objects.nonNull(client)) {
            return client;
        }
        synchronized (com.aliyun.avatar20220130.Client.class) {
            if (client == null) {
                client = createClient();
            }
        }
        return client;
    }

    public Boolean holding() throws Exception {
        String sessionId = redisTemplate.opsForValue().get(RedisKeyConstants.DIGITAL_HUMAN_SESSION_ID);

        SendCommandRequest sendCommandRequest = new SendCommandRequest()
                .setSessionId(sessionId)
                .setTenantId(tenantId)
                .setCode("INTERRUPT")
                .setUniqueCode(UUID.randomUUID().toString());
        RuntimeOptions runtime = buildRuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            SendCommandResponse sendCommandResponse = getClient().sendCommandWithOptions(sendCommandRequest, runtime);
            log.info("sendCommand resp: {}" , JacksonConverterUtil.convertObjectToJson(sendCommandResponse));
            return sendCommandResponse.body.success;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error("sendCommandWithOptions TeaException error", error.getMessage());
            // 诊断地址
            log.error("sendCommandWithOptions Recommend: " + error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.error("endCommandWithOptions Exception error", error.getMessage());
            // 诊断地址
            log.error("sendCommandWithOptions Recommend  :"+error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return Boolean.FALSE;
    }
}
