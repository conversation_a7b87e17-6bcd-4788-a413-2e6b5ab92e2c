package com.decathlon.digital.interceptor;

import com.decathlon.digital.util.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;
@Component
public class ContextInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Header解析用户信息
        String authorization = request.getHeader("Authorization");
        String profile =null;
        if (StringUtils.isNotBlank(authorization)) {
            String token = authorization.replace("Bearer ", "");
             profile = JwtUtils.getProfile(token);
        }else {
            profile = null;
        }
        RequestContextHolder.currentRequestAttributes()
                .setAttribute("profile", profile, RequestAttributes.SCOPE_REQUEST);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 自动清理，非必须但建议显式清除
        RequestContextHolder.currentRequestAttributes()
            .removeAttribute("profile", RequestAttributes.SCOPE_REQUEST);
    }


}