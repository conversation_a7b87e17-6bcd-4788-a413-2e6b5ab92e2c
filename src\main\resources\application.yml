server:
  port: 8780
  servlet:
    context-path: /ai_agent
  timeZone: ${TIME_ZONE:GMT+8}
spring:
  ai:
    openai:

      api-key: ${AI_DASHSCOPE_API_KEY:sk-2374f1a49b0548c8bba5fa63d85d04a5}
      base-url: http://ep-2zei5e066542aca8ae08.dashscope.cn-beijing.privatelink.aliyuncs.com/compatible-mode/
#      base-url: https://dashscope.aliyuncs.com/compatible-mode/
#  ai:
#    dashscope:
#      api-key: ${AI_DASHSCOPE_API_KEY:sk-2374f1a49b0548c8bba5fa63d85d04a5}
      chat:
        options:
          model: qwen-plus-latest
  main:
    allow-bean-definition-overriding: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_DB:postgres}?currentSchema=pulic
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:123456}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: HikariCP
      minimum-idle: ${DB_MIN_SIZE:1}
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: ${DB_IDLE_TIMEOUT:60000}
      connection-test-query: SELECT 1
      max-lifetime: ${DB_MAX_LIFE_TIME:60000}
      connection-timeout: ${DB_CONN_TIMIEOUT:60000}
      validation-timeout: ${VALIDATION_TIMEOUT:30000}
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      username: ${REDIS_USERNAME:}
      password: ${REDIS_PASSWORD:}
      timeout: ${REDIS_TIMEOUT:30000}
      database: ${REDIS_DATABASE:0}
      lettuce:
        pool:
          max-active: 8 # default: 8
          max-idle: 8 # default: 8
          min-idle: 0 # default: 0
  jpa:
    database: POSTGRESQL
    show-sql: ${SHOW_SQL:true}
    hibernate.ddl-auto: ${DDl_AUTO:none}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    baseline-on-migrate: true
    baseline-version: 0
    locations: classpath:db/migration/postgres
    out-of-order: true
    table: flyway_schema_history_app
    ignoreMissingMigrations: true
    cleanDisabled: true
    validateOnMigrate: false
    schemas: ai_agent
  jackson:
    # 指定时间区域类型
    time-zone: ${TIME_ZONE:GMT+8}
    # 格式化全局时间字段
    date-format: yyyy-MM-dd'T'HH:mm:ssZZ
    property-naming-strategy: SNAKE_CASE

  application:
    name: digital-ai-agent-service
  profiles:
    active: ${ACTIVE_PROFILE:local}

  task:
    execution:
      pool:
        core-size: ${CORE_SIZE:6}
        max-size: ${MAX_SIZE:20}
        queue-capacity: ${QUEUE_CAPACITY:50000}
        keep-alive: ${KEEP_ALIVE_TIME:30s}

rest:
  connect-timeout: ${REST_CONNECT_TIMEOUT:30s}
  read-timeout: ${REST_READ_TIMEOUT:60s}

proxy:
  host: ${PROXY_HOST:}
  port: ${PROXY_PORT:}

common-log:
  bootstrap-servers: ${KAFKA_LOG_HOST:localhost:9092}
  topic: ${KAFKA_TOPIC_LOG:aichatbox_logs}
  sasl-profile: ${SASL_LOG_PROFILE:xxx}
  root-level: ${LOG_LEVEL:info}


logging:
  level:
    ROOT: INFO
    org.springframework.web: ${LOGGING_WEB:INFO}
    org.apache.kafka: WARN
    com.decathlon.*: ${LOGGING_LEVEL:DEBUG}
    org.springframework.ai.chat.client.advisor: ${LOGGING_AI:DEBUG}
  pattern:
    level: "%5p [${spring.application.name:},%X{requestId:-},%X{traceId:-},%X{spanId:-}]"
  kafka:
    host: ${KAFKA_LOG_HOST:localhost:9092}


management:
  endpoints:
    web:
      exposure:
        include: '*'


ai:
  qwen:
    enable: ${AI_ENABLE:true}
    api-key: ${AI_QWEN_API_KEYf:sk-2374f1a49b0548c8bba5fa63d85d04a5}
  digitalHuman:
    accessKeyId: ${DIGITALHUMAN_ACCESSKEYID:LTAI5tLdbmwjSnudkvka7V9E}
    accessKeySecret: ${DIGITALHUMAN_ACCESSKEYSECRET:******************************}
    endpoint: ${DIGITALHUMAN_ENDPOINT:avatar.cn-zhangjiakou.aliyuncs.com}
    appId: ${DIGITALHUMAN_APPID:3d3v2a3117}
    backGroundImageUrl: ${DIGITALHUMAN_BACKGROUNDIMAGEURL:https://www.decathlon.com.cn/static/guider/bg.png}
    tenantId: ${DIGITALHUMAN_TENANTID:28963}
  proxy:
    host: ${HTTP_PROXY_HOST:proxy-internet-ali-cnsh.pp.dktapp.cloud}
    port: ${HTTP_PROXY_PORT:3128}
#    host: ${HTTP_PROXY_HOST:proxy-internet-ali-cnsh.pp.dktapp.cloud}
#    port: ${HTTP_PROXY_PORT:3128}


