package com.decathlon.digital.common.exception;


public enum BizError implements BaseError {

    EM_PRODUCT_DATA_ERROR("biz_err_1", "product {0} data error {1} from em"),
    NOT_FOUND_DATA_IN("biz_err_2", " {0} data {1} not found in {2}"),
    DUPLICATE("biz_err_3","{0} is duplicate"),
    NOT_VALID_INPUT("biz_err_4","{0} is not valid input {1}")

    ;


    private final String code;
    private final String message;

    BizError(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
