package com.decathlon.digital.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @create 2018-05-30
 */
@MappedSuperclass
@Data
public abstract class BaseTimeEntity implements Serializable {


  @Column(name = "create_time", nullable = false, updatable = false)
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
  @CreationTimestamp
  private Date createTime;

  @Column(name = "update_time", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
  @UpdateTimestamp
  private Date updateTime;

  @Override
  public String toString() {
    return "BasicEntity{" +
        "createTime=" + createTime +
        ", updateTime=" + updateTime +
        '}';
  }

}
