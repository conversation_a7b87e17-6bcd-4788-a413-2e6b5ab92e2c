package com.decathlon.digital.service.remote;

import java.util.Map;
import java.util.Optional;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.reactive.function.client.WebClient.Builder;

import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.service.remote.dify.DifyChatRequest;
import com.decathlon.digital.service.remote.dify.DifyChatResponse;
import com.decathlon.digital.service.remote.dify.Metadata;

import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service(value = "otherDifyAiService")
public class OtherDifyAiService extends DifyAiService implements AiService {

	public OtherDifyAiService(RedisTemplate<String, String> redisTemplate, Builder webClientBuilder) {
		super(redisTemplate, webClientBuilder);
	}

	@Override
	public Flowable<ChatRespDto> streamCallAi(String prompt, String appid, String webSession,
			Map<String, Object> bizParams) {
		return super.streamCallDifyAiByParam(buildDifyChatRequest(prompt,webSession),webSession)
				.map(this::convertChatResp);
	}

	public DifyChatRequest buildDifyChatRequest(String prompt,String webSession) {
		String conversationId  = super.getSessionId(webSession)==null ? "" : super.getSessionId(webSession);
		String profile = (String) RequestContextHolder.currentRequestAttributes()
                .getAttribute("profile", RequestAttributes.SCOPE_REQUEST);
		return DifyChatRequest.builder().query(prompt).user(profile).conversationId(conversationId).build();
	}

	public ChatRespDto convertChatResp(DifyChatResponse in) {
		String finishReason = "message_end".equals(in.getEvent()) ? "stop" : "";
		String text = Optional.ofNullable(in.getAnswer()).orElse(null);
		if("stop".equals(finishReason)) {
			log.info("Dify AI response finished with reason: {}", in);
		}
		Metadata metadata = Optional.ofNullable(in.getMetadata()).orElse(null);
		return ChatRespDto.builder().content(text).event(in.getEvent()).finishReason(finishReason).metadata(metadata).build();
	}

}
