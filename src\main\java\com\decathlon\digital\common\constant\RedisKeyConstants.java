package com.decathlon.digital.common.constant;

public class RedisKeyConstants {

    public static final String REDIS_KEY_PREFIX = "ai-agent:";


    public static final String CHAT_PREFIX = "chat:";
    public static final String SESSION_ID = CHAT_PREFIX + "session_id";


    public static final String DIGITAL_HUMAN_SESSION_ID = "DigitalHuman:session_id";

    public static final String CHAT_CONTEXT = CHAT_PREFIX + "chat_context:";
    public static String appendRedisKey(String prefix, String value) {
        if (prefix.endsWith(":")) {
            return prefix.concat(value);
        } else {
            return prefix.concat(":").concat(value);
        }

    }
}
