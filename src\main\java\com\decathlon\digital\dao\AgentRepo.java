package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.domain.agent.AiServiceType;
import com.decathlon.digital.domain.agent.entity.AiAgent;
import com.decathlon.digital.domain.agent.entity.AgentCategory;
import com.decathlon.digital.service.remote.AiService;
import com.decathlon.digital.service.remote.OtherDifyAiService;
import com.decathlon.digital.service.remote.OtherQwenAiSerivce;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Agent数据访问层，支持数据库加载和缓存
 *
 * <AUTHOR> Lei
 * @since 2025-05-06
 */
@Repository
@RequiredArgsConstructor
public class AgentRepo {

	private final OtherQwenAiSerivce otherQwenAiSerivce;
	private final OtherDifyAiService otherDifyAiService;
	private final AiAgentRepository aiAgentRepository;
	private final AgentCategoryRepository agentCategoryRepository;

	// AI服务映射缓存
	private final Map<AiServiceType, AiService> aiServiceMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void initAiServiceMap() {
        aiServiceMap.put(AiServiceType.QWEN, otherQwenAiSerivce);
        aiServiceMap.put(AiServiceType.DIFY, otherDifyAiService);
        System.out.println("AI服务映射初始化完成: " + aiServiceMap.keySet());
    }

    /**
     * 获取所有激活的Agent列表
     *
     * @return Agent DTO列表
     */
    public List<AiAgentDto> getAllAgents() {
        List<AiAgent> agents = aiAgentRepository.findAllActiveOrderBySortOrder();
        return agents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据Agent代码查找Agent
     *
     * @param agentCode Agent代码
     * @return Agent DTO
     */
    public AiAgentDto findByCode(String agentCode) {
        return aiAgentRepository.findByAgentCode(agentCode)
                .filter(agent -> agent.getIsActive())
                .map(this::convertToDto)
                .orElse(null);
    }

    /**
     * 根据分类ID获取Agent列表
     *
     * @param categoryId 分类ID
     * @return Agent DTO列表
     */
    public List<AiAgentDto> getAgentsByCategory(Long categoryId) {
        List<AiAgent> agents = aiAgentRepository.findActiveByCategoryIdOrderBySortOrder(categoryId);
        return agents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取默认显示的Agent列表
     *
     * @return 默认Agent DTO列表
     */
    public List<AiAgentDto> getDefaultAgents() {
        List<AiAgent> agents = aiAgentRepository.findDefaultAgentsOrderBySortOrder();
        return agents.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有激活的分类列表
     *
     * @return 分类列表
     */
    public List<AgentCategory> getAllActiveCategories() {
        return agentCategoryRepository.findAllActiveOrderBySortOrder();
    }

    /**
     * 将实体转换为DTO
     *
     * @param agent Agent实体
     * @return Agent DTO
     */
    private AiAgentDto convertToDto(AiAgent agent) {
        AiService aiService = aiServiceMap.get(agent.getAiServiceType());
        if (aiService == null) {
            System.out.println("未找到对应的AI服务: " + agent.getAiServiceType());
        }

        return AiAgentDto.builder()
                .agentCode(agent.getAgentCode())
                .agentName(agent.getAgentName())
                .agentType(agent.getAgentType())
                .remoteAppId(agent.getRemoteAppId())
                .aiService(aiService)
                .agentIcon(agent.getAgentIcon())
                .description(agent.getDescription())
                .categoryId(agent.getCategory().getId())
                .categoryName(agent.getCategory().getCategoryName())
                .categoryIcon(agent.getCategory().getCategoryIcon())
                .isActive(agent.getIsActive())
                .isDefault(agent.getIsDefault())
                .sortOrder(agent.getSortOrder())
                .build();
    }
}
