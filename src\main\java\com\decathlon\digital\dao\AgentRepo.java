package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.AIAgentType;
import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.domain.agent.AiServiceType;
import com.decathlon.digital.domain.agent.entity.AiAgent;
import com.decathlon.digital.domain.agent.entity.AgentCategory;
import com.decathlon.digital.service.remote.AiService;
import com.decathlon.digital.service.remote.OtherDifyAiService;
import com.decathlon.digital.service.remote.OtherQwenAiSerivce;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Agent数据访问层，支持数据库加载和缓存
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AgentRepo {

	private final OtherQwenAiSerivce otherQwenAiSerivce;
	private final OtherDifyAiService otherDifyAiService;
	private final AiAgentRepository aiAgentRepository;
	private final AgentCategoryRepository agentCategoryRepository;

	// AI服务映射缓存
	private final Map<AiServiceType, AiService> aiServiceMap = new ConcurrentHashMap<>();

    @PostConstruct
    private void initAiServiceMap() {
        aiServiceMap.put(AiServiceType.QWEN, otherQwenAiSerivce);
        aiServiceMap.put(AiServiceType.DIFY, otherDifyAiService);
    }

    /**
     * 根据Agent代码查找Agent
     *
     * @param agentCode Agent代码
     * @return Agent DTO
     */
    public AiAgentDto findByCode(String agentCode) {
        return aiAgentRepository.findByAgentCode(agentCode)
                .filter(agent -> agent.getIsActive())
                .map(this::convertToDto)
                .orElse(null);
    }

    /**
     * 根据分类ID获取所有Agent列表（包含非激活的）
     *
     * @param categoryId 分类ID
     * @return Agent DTO列表
     */
    public List<AiAgentDto> getAllAgentsByCategory(Long categoryId) {
        List<AiAgent> agents = aiAgentRepository.findByCategoryIdOrderBySortOrder(categoryId);
        return agents.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * 获取所有激活的分类列表
     *
     * @return 分类列表
     */
    public List<AgentCategory> getAllActiveCategories() {
        return agentCategoryRepository.findAllActiveOrderBySortOrder();
    }

    /**
     * 将实体转换为DTO
     *
     * @param agent Agent实体
     * @return Agent DTO
     */
    private AiAgentDto convertToDto(AiAgent agent) {
        AiService aiService = aiServiceMap.get(AiServiceType.valueOf(agent.getAiServiceType()));
        if (aiService == null) {
            log.warn("No AI service found for type: {}", agent.getAiServiceType());
			return null; // 或者抛出异常，视具体需求而定
        }

        return AiAgentDto.builder()
                .agentCode(agent.getAgentCode())
                .agentName(agent.getAgentName())
                .agentType(AIAgentType.valueOf(agent.getAgentType()))
                .key(agent.getKey())
                .aiService(aiService)
                .agentIcon(agent.getAgentIcon())
                .description(agent.getDescription())
                .categoryId(agent.getCategory().getId())
                .categoryName(agent.getCategory().getCategoryName())
                .categoryIcon(agent.getCategory().getCategoryIconName())
                .label(agent.getLabel())
                .isActive(agent.getIsActive())
                .isDefault(agent.getIsDefault())
                .sortOrder(agent.getSortOrder())
                .build();
    }
}
