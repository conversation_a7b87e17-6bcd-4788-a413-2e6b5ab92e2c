package com.decathlon.digital.dao;

import com.decathlon.digital.domain.agent.AIAgentType;
import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.service.remote.OtherDifyAiService;
import com.decathlon.digital.service.remote.OtherQwenAiSerivce;

import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;

import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @auther Shi Lei
 * @since 2025-05-06
 */
@Repository
@AllArgsConstructor
public class AgentRepo {
	
	private final OtherQwenAiSerivce otherQwenAiSerivce;
	private final OtherDifyAiService otherDifyAiService;

    private List<AiAgentDto> agents = new ArrayList<>(3);

    @PostConstruct
    private void initAgents() {
        agents.add(
                AiAgentDto.builder().agentCode("hr-helper").agentName("HR助手").agentType(AIAgentType.CHAT).aiService(otherQwenAiSerivce) .remoteAppId("f61e59f4dc48412a9929a34d37e698af").build()
        );
        
        agents.add(
                AiAgentDto.builder().agentCode("op-helper").agentName("OP助手").agentType(AIAgentType.CHAT).aiService(otherDifyAiService) .remoteAppId("app-x3sek6y2t3OItJDwzjhLrn8W").build()
        );
    }

    public List<AiAgentDto> getAllAgents() {
        return agents;
    }

    public AiAgentDto findByCode(String agentCode) {
        return agents.stream().filter(agent -> agent.getAgentCode().equals(agentCode)).findFirst().orElse(null);
    }
}
