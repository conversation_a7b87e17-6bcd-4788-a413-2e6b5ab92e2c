package com.decathlon.digital.domain.conversation.dto;

import com.decathlon.digital.common.dto.BaseTimeDto;
import jakarta.persistence.Column;
import jakarta.persistence.Enumerated;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.ai.chat.messages.MessageType;

import java.util.Map;

/**
 * @auther Shi Lei
 * @since 2025-05-16
 */
@Data
public class ChatConversationDto extends BaseTimeDto {
    private Long id;

    private String content;

    private MessageType messageType;

    private String chatSessionId;

    private Map<String,Object> metadata;
}
