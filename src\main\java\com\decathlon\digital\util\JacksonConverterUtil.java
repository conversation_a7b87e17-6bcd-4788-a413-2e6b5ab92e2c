package com.decathlon.digital.util;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-05-16
 */
public class JacksonConverterUtil {

    private static ObjectMapper mapper;

    private JacksonConverterUtil() {
    }


    private static void init() {
        mapper = new ObjectMapper();
        //ignore null properties
        mapper.setSerializationInclusion(Include.NON_NULL);
        //the first letter upper and camel case
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        mapper.enable(MapperFeature.USE_STD_BEAN_NAMING);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    }


    public static ObjectMapper getObjectMapper() {
        if (mapper == null) {
            init();
        }
        return mapper;
    }


    public static String convertObjectToJson(Object o) throws JsonProcessingException {
        return getObjectMapper().writeValueAsString(o);
    }

    public static <T> T convertJsonToObject(String json, Class<T> clazz) throws IOException {
        return getObjectMapper().readValue(json, clazz);
    }

    public static Map<String, String> objectToMap(Object o) { //
        return getObjectMapper().convertValue(o, Map.class);
    }
    public static <T> T mapToObject(Map m, Class<T> clazz) { //
        return getObjectMapper().convertValue(m, clazz);
    }

}
