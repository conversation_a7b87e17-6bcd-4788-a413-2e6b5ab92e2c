package com.decathlon.digital.common;

import com.decathlon.digital.common.dto.BaseApiResponseDto;
import com.decathlon.digital.common.exception.BaseException;
import com.decathlon.digital.common.exception.SystemError;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR> Lei
 * @create 2020-06-02
 */
@ControllerAdvice
@Slf4j
public class ControllerExceptionHandler extends ResponseEntityExceptionHandler {


    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest request) {
        log.error("handleHttpRequestMethodNotSupported {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED)
                .body(BaseApiResponseDto.buildFromError(SystemError.INPUT_CONVERT_ERROR));
    }

    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotSupported(
            HttpMediaTypeNotSupportedException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest request) {
        log.error("handleHttpMediaTypeNotSupported {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
                .body(BaseApiResponseDto.buildFromError(SystemError.INPUT_CONVERT_ERROR));
    }

    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotAcceptable(
            HttpMediaTypeNotAcceptableException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest request) {
        log.error("handleHttpMediaTypeNotAcceptable {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE)
                .body(BaseApiResponseDto.buildFromError(SystemError.INPUT_CONVERT_ERROR));
    }


    @Override
    protected ResponseEntity<Object> handleServletRequestBindingException(
            ServletRequestBindingException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest request) {
        log.error("handleServletRequestBindingException {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto.buildFromError(SystemError.INPUT_CONVERT_ERROR));
    }


    @Override
    protected ResponseEntity<Object> handleTypeMismatch(TypeMismatchException ex, HttpHeaders headers,
                                                        HttpStatusCode status, WebRequest request) {
        log.error("handleTypeMismatch {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto.buildFromError(SystemError.INPUT_CONVERT_ERROR));
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleHttpMessageNotReadable {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto
                        .buildFromError(SystemError.INPUT_CONVERT_ERROR, (ex.getMessage())));
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotWritable(HttpMessageNotWritableException ex,
                                                                  HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleHttpMessageNotWritable {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto
                        .buildFromError(SystemError.INPUT_CONVERT_ERROR, (ex.getMessage())));
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.error("handleMethodArgumentNotValid {}", ex.getMessage());
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        String errMsg = null;
        if (fieldErrors != null) {
            StringBuffer buffer = new StringBuffer();
            fieldErrors.parallelStream()
                    .forEach(fieldError -> buffer.append(fieldError.getField()).append(":")
                            .append(fieldError.getDefaultMessage()).append("; "));
            errMsg = buffer.toString();
        } else {
            errMsg = ex.getMessage();
        }
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto
                        .fail(SystemError.INPUT_CONVERT_ERROR.getCode(), errMsg));
    }

    public static String getErrorMessage(BindingResult result) {
        List<FieldError> fieldErrors = result.getFieldErrors();
        StringBuffer buffer = new StringBuffer();
        fieldErrors.parallelStream()
                .forEach(fieldError -> buffer.append(fieldError.getDefaultMessage()).append("; "));
        return buffer.toString();
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestPart(
            MissingServletRequestPartException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest request) {
        log.error("handleMissingServletRequestPart {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(BaseApiResponseDto
                        .buildFromError(SystemError.INPUT_CONVERT_ERROR, (ex.getMessage())));
    }

//    @Override
//    protected ResponseEntity<Object> handleBindException(BindException ex, HttpHeaders headers,
//                                                         HttpStatusCode status, WebRequest request) {
//        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
//                .body(BaseApiResponseDto.fail(SystemError.INPUT_CONVERT_ERROR.getCode(), ex.getMessage()));
//    }

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex,
                                                                   HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(BaseApiResponseDto.fail(SystemError.NOT_FOUND.getCode(), ex.getMessage()));
    }

    @Override
    protected ResponseEntity<Object> handleAsyncRequestTimeoutException(
            AsyncRequestTimeoutException ex, HttpHeaders headers, HttpStatusCode status,
            WebRequest webRequest) {
        return ResponseEntity.status(HttpStatus.GATEWAY_TIMEOUT)
                .body(BaseApiResponseDto.fail(SystemError.SYSTEM_INTERNAL_ERROR.getCode(), ex.getMessage()));
    }
    //
    //  @ExceptionHandler(value = {UnAuthException.class})
    //  public ResponseEntity<BaseResponse> handleAuthException(UnAuthException e) {
    //    return ResponseEntity.status(e.getHttpStatus())
    //        .body(BaseResponse.fail(e.getErrorCode(), e.getErrorMsg()));
    //  }

    @ExceptionHandler(value = {ExecutionException.class})
    public ResponseEntity<Object> handleExecutionException(ExecutionException e) {
        log.error("handleExecutionException ", e);
        return ResponseEntity.status(HttpStatus.GATEWAY_TIMEOUT).body(
                BaseApiResponseDto.fail(SystemError.SYSTEM_INTERNAL_ERROR.getCode(), e.getMessage()));
    }

    @ExceptionHandler(value = {JsonProcessingException.class})
    public ResponseEntity<BaseApiResponseDto> handleJsonProcessingException(
            JsonProcessingException e) {
        logger.error("handleJsonProcessingException ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BaseApiResponseDto.buildFromError(SystemError.SYSTEM_INTERNAL_ERROR));
    }


    @ExceptionHandler(value = {BaseException.class})
    public ResponseEntity<BaseApiResponseDto> handleException(BaseException e) {
        logger.error(e.getMessage(), e);
        return ResponseEntity.status(e.getHttpStatus())
                .body(BaseApiResponseDto.fail(e.getCode(), e.getMessage()));
    }


    //
    @ExceptionHandler(value = {Exception.class})
    public ResponseEntity<BaseApiResponseDto> handleException(Exception e) {
        logger.error("handle basic exception ", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BaseApiResponseDto.buildFromError(SystemError.SYSTEM_INTERNAL_ERROR));
    }


}