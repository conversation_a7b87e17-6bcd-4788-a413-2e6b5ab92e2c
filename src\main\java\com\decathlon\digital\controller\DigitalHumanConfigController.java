package com.decathlon.digital.controller;

import com.aliyun.avatar20220130.models.QueryRunningInstanceResponseBody;
import com.aliyun.avatar20220130.models.StartInstanceResponseBody;
import com.decathlon.digital.common.dto.BaseApiResponseDto;
import com.decathlon.digital.service.chat.DigitalHumanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @auther Shi Lei
 * @since 2025-04-17
 */
@Slf4j
@RestController
@Validated
@RequestMapping("api/v1/digital-human/config")
@RequiredArgsConstructor
public class DigitalHumanConfigController {
    private final DigitalHumanService digitalHumanService;

    @GetMapping("/start_instance")
    public BaseApiResponseDto<StartInstanceResponseBody> startInstance() throws Exception {
        try {
            StartInstanceResponseBody startInstanceResponseBody = digitalHumanService.startInstance();
            return BaseApiResponseDto.ok(startInstanceResponseBody);
        } catch (Exception e) {
            return BaseApiResponseDto.fail("startInstance", e.getMessage());
        }
    }

    @PostMapping("/stop_instance")
    public BaseApiResponseDto<Boolean> stopInstance() throws Exception {
        try {
            Boolean isStop = digitalHumanService.stopInstance();
            return BaseApiResponseDto.ok(isStop);
        } catch (Exception e) {
            return BaseApiResponseDto.fail("stopInstance", e.getMessage());
        }
    }

    @GetMapping("/query_instance")
    public BaseApiResponseDto<List<QueryRunningInstanceResponseBody.QueryRunningInstanceResponseBodyData>> queryInstance() throws Exception {
        try {
            List<QueryRunningInstanceResponseBody.QueryRunningInstanceResponseBodyData> queryRunningInstanceResponseBodyData = digitalHumanService.queryRunningInstance();
            return BaseApiResponseDto.ok(queryRunningInstanceResponseBodyData);
        } catch (Exception e) {
            return BaseApiResponseDto.fail("queryInstance", e.getMessage());
        }
    }

    @PostMapping("/send_message")
    public BaseApiResponseDto<Boolean> sendMessage(@RequestParam("text") String text) throws Exception {
        try {
            digitalHumanService.sendMessage(text);
            return BaseApiResponseDto.ok(true);
        } catch (Exception e) {
            return BaseApiResponseDto.fail("sendMessage", e.getMessage());
        }
    }

    @PostMapping("/holding")
    public BaseApiResponseDto<Boolean> holding() throws Exception {
        try {
            Boolean isHolding = digitalHumanService.holding();
            return BaseApiResponseDto.ok(isHolding);
        } catch (Exception e) {
            return BaseApiResponseDto.fail("holding", e.getMessage());
        }
    }
}
