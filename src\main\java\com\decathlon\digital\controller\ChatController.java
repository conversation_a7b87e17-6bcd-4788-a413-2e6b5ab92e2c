package com.decathlon.digital.controller;

import cn.hutool.core.date.DateUtil;
import com.decathlon.digital.common.dto.BaseApiResponseDto;
import com.decathlon.digital.common.exception.BaseException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.config.SpringDocConfig;
import com.decathlon.digital.domain.chat.ChatInputReqDto;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.function.ShopingGuidInputDto;
import com.decathlon.digital.service.chat.ChatService;
import com.decathlon.digital.service.chat.DigitalHumanService;
import io.reactivex.Flowable;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

/**
 * @auther Shi Lei
 * @since 2025-03-07
 */
@Slf4j
@RestController
@Validated
@RequestMapping("api/v1/chats")
@RequiredArgsConstructor
@SecurityRequirement(name = SpringDocConfig.BEARER_AUTH)
public class ChatController {


    private final ChatService chatService;

    private final DigitalHumanService digitalHumanService;


    @Deprecated
    public BaseApiResponseDto<ChatRespDto> shoppingGuides(@RequestBody ShopingGuidInputDto dto,
                                                          @RequestParam(defaultValue = "true") Boolean enableSpeak) {
        log.info("chat start time -> {}", DateUtil.now());
        ChatRespDto chat = chatService.shoppingGuides(dto, enableSpeak);
        log.info("chat finish time -> {}", DateUtil.now());
        return BaseApiResponseDto.ok(chat);
    }


    @PostMapping("/free")
    public BaseApiResponseDto<ChatRespDto> chatFree(@RequestBody @Validated ChatInputReqDto dto) {

        log.info("chat start time -> {}", DateUtil.now());
        ChatRespDto res = chatService.chatFree(dto);
        log.info("chat finish time -> {}", DateUtil.now());
        return BaseApiResponseDto.ok(res);
    }


    @PostMapping(value = "/free/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    Flux<ServerSentEvent<BaseApiResponseDto<ChatRespDto>>>
    public Flux<BaseApiResponseDto<ChatRespDto>> chatFreeByStreamWithInterrupt(@RequestBody ChatInputReqDto dto) {
        Flux<ChatRespDto> res = null;
        try {
            res = chatService.chatFreeByStream(dto);
            return res.map(BaseApiResponseDto::ok);
        } catch (BaseException e) {
            return Flux.just(BaseApiResponseDto.fail(e.getCode(), e.getMessage()));
        } catch (Exception e) {
            return Flux.just(BaseApiResponseDto.fail(SystemError.SYSTEM_INTERNAL_ERROR.getCode(), e.getMessage()));
        }
    }


    @PostMapping("/interrupt")
    public ResponseEntity<Void> interrupt(@RequestParam String session) {
        chatService.interrupt(session);
        return ResponseEntity.ok().build();
    }


}
