package com.decathlon.digital.common.exception;

import org.springframework.http.HttpStatus;

/**
 * @auther <PERSON>
 * @since 2024-12-02
 */
public class AuthException extends BaseException {

    public AuthException(BaseError baseError) {
        super(baseError);
    }

    public AuthException(BaseError baseError, Object... params) {
        super(baseError, params);
    }

    protected AuthException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public AuthException(String message) {
        super(message);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public HttpStatus getHttpStatus() {
        return HttpStatus.UNAUTHORIZED;
    }
}
