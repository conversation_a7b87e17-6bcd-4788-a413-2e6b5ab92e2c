package com.decathlon.digital.controller;

import com.decathlon.digital.common.dto.BaseApiResponseDto;
import com.decathlon.digital.common.dto.PageRespDTO;
import com.decathlon.digital.common.dto.PageableQueryDto;
import com.decathlon.digital.common.dto.SortQueryDto;
import com.decathlon.digital.config.SpringDocConfig;
import com.decathlon.digital.domain.agent.AgentContants;
import com.decathlon.digital.domain.conversation.dto.ChatConversationDto;
import com.decathlon.digital.domain.conversation.dto.ChatSessionDto;
import com.decathlon.digital.domain.conversation.dto.ChatSessionEditDto;
import com.decathlon.digital.service.chat.ChatConversionService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @auther <PERSON>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("api/v1/sessions")
@RequiredArgsConstructor
@SecurityRequirement(name = SpringDocConfig.BEARER_AUTH)
public class ConversionController {

    private final ChatConversionService chatConversionService;

    @PostMapping("/")
    public BaseApiResponseDto<ChatSessionDto> createSession(
            @RequestParam(required = false, defaultValue = AgentContants.DEFAULT_CHAT_AGENT_CODE) String agentCode,
            @RequestBody @Valid ChatSessionEditDto chatSessionEditDto) {
        ChatSessionDto session = chatConversionService.createSession(agentCode,chatSessionEditDto);
        return BaseApiResponseDto.ok(session);
    }


    @GetMapping("/page")
    public BaseApiResponseDto<PageRespDTO<ChatSessionDto>> pageSession(
            @RequestParam(required = false, value = "agent_code") String agentCode,
            @RequestParam(required = false, value = "include_deleted", defaultValue = "false") Boolean includeDeleted,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false, value = "sort") String sort,
            @Parameter(example = "DESC", description = "default DESC,inculde: ASC, DESC; ")
            @RequestParam(value = "sort_type", defaultValue = "DESC") Sort.Direction sortType) {
        PageableQueryDto pageableQueryDto = new PageableQueryDto(pageNum, pageSize, sort, sortType);
        PageRespDTO<ChatSessionDto> page = chatConversionService.pageSession(agentCode, includeDeleted, pageableQueryDto);
        return BaseApiResponseDto.ok(page);
    }

    @GetMapping("/")
    public BaseApiResponseDto<List<ChatSessionDto>> listSession(
            @RequestParam(required = false, value = "agent_code") String agentCode,
            @RequestParam(required = false, value = "include_deleted", defaultValue = "false") Boolean includeDeleted,
            @RequestParam(required = false, value = "sort") String sort,
            @Parameter(example = "DESC", description = "default DESC,inculde: ASC, DESC; ")
            @RequestParam(value = "sort_type", defaultValue = "DESC") Sort.Direction sortType) {
        SortQueryDto sortQueryDto = new SortQueryDto(sort, sortType);
        List<ChatSessionDto> res = chatConversionService.listSession(agentCode, includeDeleted, sortQueryDto);
        return BaseApiResponseDto.ok(res);
    }

    @DeleteMapping("/{chat_session_id}")
    public BaseApiResponseDto<Void> deleteSession(@PathVariable("chat_session_id") Long sessionId) {
        chatConversionService.deleteSession(sessionId);
        return BaseApiResponseDto.ok(null);
    }

    @PatchMapping("/{chat_session_id}")
    public BaseApiResponseDto<ChatSessionDto> updateContentSummaryForSession(@PathVariable("chat_session_id") Long sessionId, @RequestBody ChatSessionEditDto chatSessionEditDto) {
        ChatSessionDto chatSessionDto = chatConversionService.updateContentSummaryForSession(sessionId, chatSessionEditDto);
        return BaseApiResponseDto.ok(chatSessionDto);
    }

    @GetMapping("/{chat_session_id}/conversations/page")
    public BaseApiResponseDto<PageRespDTO<ChatConversationDto>> pageConversationsBySessionId(
            @PathVariable("chat_session_id") Long sessionId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false, value = "sort") String sort,
            @Parameter(example = "DESC", description = "default DESC,inculde: ASC, DESC; ")
            @RequestParam(value = "sort_type", defaultValue = "DESC") Sort.Direction sortType) {
        PageableQueryDto pageableQueryDto = new PageableQueryDto(pageNum, pageSize, sort, sortType);
        PageRespDTO<ChatConversationDto> page = chatConversionService.pageConversationsBySessionId(sessionId, pageableQueryDto);
        return BaseApiResponseDto.ok(page);
    }

    @GetMapping("/{chat_session_id}/conversations")
    public BaseApiResponseDto<List<ChatConversationDto>> listConversationsBySessionId(
            @PathVariable("chat_session_id") Long sessionId,
            @RequestParam(required = false, value = "sort") String sort,
            @Parameter(example = "DESC", description = "default DESC,inculde: ASC, DESC; ")
            @RequestParam(value = "sort_type", defaultValue = "DESC") Sort.Direction sortType) {
        SortQueryDto sortQueryDto = new SortQueryDto(sort, sortType);
        List<ChatConversationDto> respDTO = chatConversionService.listConversationsBySessionId(sessionId, sortQueryDto);
        return BaseApiResponseDto.ok(respDTO);
    }

    @DeleteMapping("/{chat_session_id}/conversations/{conversation_id}")
    public BaseApiResponseDto<Void> deleteConversation(
            @PathVariable("chat_session_id") Long sessionId,
            @PathVariable("conversation_id") Long conversationId) {
        chatConversionService.deleteConversation(sessionId, conversationId);
        return BaseApiResponseDto.ok(null);
    }

}
