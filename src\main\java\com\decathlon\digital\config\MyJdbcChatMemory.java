package com.decathlon.digital.config;

import com.decathlon.digital.dao.ChatConversationRepository;
import com.decathlon.digital.domain.conversation.entity.ChatConversation;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @auther Shi Lei
 * @since 2025-05-14
 */
@Component
public class MyJdbcChatMemory implements ChatMemory {

    private final ChatConversationRepository chatConversationRepository;

    public MyJdbcChatMemory(ChatConversationRepository chatConversationRepository) {
        this.chatConversationRepository = chatConversationRepository;
    }


    @Override
    public void add(String conversationId, List<Message> messages) {
        PageRequest pageRequest = PageRequest.of(0, 1, Sort.by("id").descending());
        Long sessionId = Long.valueOf(conversationId);
        //        List<ChatConversation>  = chatConversationRepository.findBySessionId(sessionId, pageRequest);

        for (Message message : messages) {
            if (MessageType.ASSISTANT.equals(message.getMessageType())) {
                //修改最后一条消息内容
                Optional<Long> lastDraftIdBySessionId = chatConversationRepository.findLastDraftIdBySessionId(sessionId, pageRequest);
                lastDraftIdBySessionId
                        .ifPresent(id ->
                                chatConversationRepository.updateLastMessage(id, message.getText(), message.getMetadata()));

            } else if (MessageType.USER.equals(message.getMessageType())) {
                //不做任何操作
                continue;
            }
            //            ChatConversation record = new ChatConversation();
            //            record.setMessageType(message.getMessageType());
            //            record.setContent(message.getText());
            //            //            Long sessionId = Long.valueOf(conversationId);
            //            record.setChatSessionId(sessionId);
            //            record.setMetadata(message.getMetadata());
            //            chatConversationRepository.save(record);
        }

    }

    @Override
    public List<Message> get(String conversationId, int lastN) {
        if (lastN <= 0) {
            return new ArrayList<>();
        }
        PageRequest pageRequest = PageRequest.of(0, lastN, Sort.by("id").descending());

        Long sessionId = Long.valueOf(conversationId);
        List<ChatConversation> bySessionId = chatConversationRepository.findBySessionId(sessionId, pageRequest);
        List<Message> res = new ArrayList<>();
        for (ChatConversation chatConversation : bySessionId) {

            if (chatConversation != null) {
                if (MessageType.ASSISTANT.equals(chatConversation.getMessageType())) {
                    res.add(new AssistantMessage(chatConversation.getContent(), chatConversation.getMetadata()));
                } else if (MessageType.USER.equals(chatConversation.getMessageType())) {
                    res.add(new UserMessage(chatConversation.getContent()));
                }
            }
        }//end for
        return res;
    }

    @Override
    public void clear(String conversationId) {
        Long sessionId = Long.valueOf(conversationId);
        chatConversationRepository.updateAsDeletedBySessionId(sessionId);
    }
}
