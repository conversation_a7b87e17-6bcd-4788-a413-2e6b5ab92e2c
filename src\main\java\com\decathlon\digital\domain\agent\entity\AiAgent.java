package com.decathlon.digital.domain.agent.entity;

import com.decathlon.digital.domain.BaseTimeEntity;
import com.decathlon.digital.domain.agent.AIAgentType;
import com.decathlon.digital.domain.agent.AiServiceType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.EnumType;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI Agent实体类
 * 
 * <AUTHOR> <PERSON>
 * @since 2025-07-24
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ai_agent", schema = "ai_agent")
public class AiAgent extends BaseTimeEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Agent代码，唯一标识
     */
    @Column(name = "agent_code", nullable = false, unique = true, length = 50)
    private String agentCode;
    
    /**
     * Agent名称
     */
    @Column(name = "agent_name", nullable = false, length = 100)
    private String agentName;
    
    /**
     * Agent类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "agent_type", nullable = false)
    private AIAgentType agentType;
    
    /**
     * 所属分类
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private AgentCategory category;
    
    /**
     * Agent图标路径
     */
    @Column(name = "agent_icon")
    private String agentIcon;
    
    /**
     * Agent描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 远程应用ID
     */
    @Column(name = "remote_app_id")
    private String remoteAppId;
    
    /**
     * AI服务类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "ai_service_type", nullable = false)
    private AiServiceType aiServiceType;
    
    /**
     * 是否可用
     */
    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;
    
    /**
     * 是否为默认显示
     */
    @Column(name = "is_default")
    @Builder.Default
    private Boolean isDefault = false;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;
}
