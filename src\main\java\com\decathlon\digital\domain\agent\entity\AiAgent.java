package com.decathlon.digital.domain.agent.entity;

import com.decathlon.digital.domain.BaseTimeEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AI Agent实体类
 * 
 * <AUTHOR> <PERSON>
 * @since 2025-07-24
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=false)
@Table(name = "ai_agent")
public class AiAgent extends BaseTimeEntity {
	
	private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Agent代码，唯一标识
     */
    @Column(name = "agent_code", nullable = false, unique = true, length = 50)
    private String agentCode;
    
    /**
     * Agent名称
     */
    @Column(name = "agent_name", nullable = false, length = 100)
    private String agentName;
    
    /**
     * Agent类型
     */
    private String agentType;
    
    /**
     * 所属分类
     */
    @JoinColumn(name = "category_id", nullable = false)
    private AgentCategory category;
    
    /**
     * Agent图标路径
     */
    @Column(name = "agent_icon")
    private String agentIcon;
    
    /**
	 * Agent标签
	 */
    private String label;
    
    /**
     * 权限code 
     */
    private String permissionCode; 
    
    /**
     * Agent描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 远程应用ID
     */
    @Column(name = "key")
    private String key;
    
    /**
     * AI服务类型
     */
    private String aiServiceType;
    
    /**
     * 是否可用
     */
    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;
    
    /**
     * 是否为默认显示
     */
    @Column(name = "is_default")
    @Builder.Default
    private Boolean isDefault = false;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;
}
