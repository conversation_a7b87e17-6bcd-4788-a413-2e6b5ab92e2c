package com.decathlon.digital.domain.agent;

import com.decathlon.digital.domain.chat.ChatInputReqDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Map;

/**
 * @auther <PERSON>
 * @since 2025-03-07
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentInputReqDto extends ChatInputReqDto {

    private String agentCode;

    private Map<String, Object> parameters; // <key, value>
}
