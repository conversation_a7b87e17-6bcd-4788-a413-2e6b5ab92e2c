package com.decathlon.digital.domain.function;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @auther <PERSON>
 * @since 2025-03-13
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DigitalHumanChatRespDto {
    private List<String> intention;
    private String content;
    private String emotion;
    private String dsmCodeJudge;
    private String searchKeyword;
    private Map<String, Object> weather;
}
