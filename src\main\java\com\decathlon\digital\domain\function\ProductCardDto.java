package com.decathlon.digital.domain.function;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @auther Shi Lei
 * @since 2025-03-11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProductCardDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @SerializedName("dsmCode")
    private String dsmCode;

    @SerializedName("dsmTitle")
    private String dsmTitle;

    @SerializedName("modelCode")
    private String modelCode;

    @SerializedName("randomItemCode")
    private String randomItemCode;

    @SerializedName("brandDisplayName")
    private String brandDisplayName = "BrandName";

    //    private BffSearchResponse.BffSearchLabelDTO label;

    //    @SerializedName("modelCount")
    //    private int modelCount;

    private BffSearchPriceDTO price;

    @SerializedName("mainImage")
    private String mainImage;

    private List<String> images;

//    private List<BenefitDto> benefits;




    //    @SneakyThrows
    //    private BffSearchResponse.BffSearchLabelDTO buildSearchLabel(SearchResponse.ModelDTO modelDTO) {
    //        BffSearchResponse.BffSearchLabelDTO bffSearchLabelDTO = new BffSearchResponse.BffSearchLabelDTO();
    //        if (modelDTO.getStickers() == null || modelDTO.getStickers().isEmpty()) {
    //            return bffSearchLabelDTO;
    //        }
    //        SearchResponse.StickerDTO stickerDTO = modelDTO.getStickers().get(0);
    //        bffSearchLabelDTO.setText(stickerDTO.getText());
    //        bffSearchLabelDTO.setBackgroundColor(stickerDTO.getBackgroundColor());
    //        bffSearchLabelDTO.setForegroundColor(stickerDTO.getForegroundColor());
    //        return bffSearchLabelDTO;
    //    }

}
