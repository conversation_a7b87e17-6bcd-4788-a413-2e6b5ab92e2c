package com.decathlon.digital.service.chat;

import cn.hutool.core.lang.UUID;
import com.decathlon.digital.common.dto.PageRespDTO;
import com.decathlon.digital.common.dto.PageableQueryDto;
import com.decathlon.digital.common.dto.SortQueryDto;
import com.decathlon.digital.common.exception.AuthException;
import com.decathlon.digital.common.exception.IllegaInputException;
import com.decathlon.digital.common.exception.SystemError;
import com.decathlon.digital.dao.ChatConversationRepository;
import com.decathlon.digital.dao.ChatSessionRepo;
import com.decathlon.digital.domain.chat.ChatInputReqDto;
import com.decathlon.digital.domain.conversation.ConversationStatus;
import com.decathlon.digital.domain.conversation.dto.ChatConversationDto;
import com.decathlon.digital.domain.conversation.dto.ChatSessionDto;
import com.decathlon.digital.domain.conversation.dto.ChatSessionEditDto;
import com.decathlon.digital.domain.conversation.dto.ConversationPairDto;
import com.decathlon.digital.domain.conversation.entity.ChatConversation;
import com.decathlon.digital.domain.conversation.entity.ChatSession;
import com.decathlon.digital.util.BeanCopyUtil;
import jakarta.persistence.criteria.Predicate;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @auther Shi Lei
 * @since 2025-05-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatConversionService {
    private final ChatSessionRepo chatSessionRepo;
    private final ChatConversationRepository chatConversationRepository;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createSessionByProfile(ChatInputReqDto input) {
        Long sessionId = input.getChatSessionId();
        // 获取请求级变量
        String profile = checkProfile();
        Optional<ChatSession> byId = chatSessionRepo.findByIdAndNotDeleted(sessionId);
        ChatSession chatSession = byId.orElseThrow(() -> new IllegaInputException(SystemError.NOT_FOUND, "sessionId", sessionId));
        if (!profile.equals(chatSession.getProfile())) {
            throw new AuthException(SystemError.ACCESS_DENIED);
        }
        chatSession.setUpdateTime(new Date());
        chatSessionRepo.save(chatSession);
    }

    private static String getProfile() {
        String profile = (String) RequestContextHolder.currentRequestAttributes()
                .getAttribute("profile", RequestAttributes.SCOPE_REQUEST);
        return profile;
    }

    public static String buildContentSummary(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        if (input.length() > maxLength) {
            // 如果字符串长度超过最大长度，截断并添加省略号
            return input.substring(0, maxLength - 3) + "...";
        }
        // 如果字符串长度不超过最大长度，直接返回原字符串
        return input;
    }

    public PageRespDTO<ChatSessionDto> pageSession(String agentCode, Boolean includeDeleted, PageableQueryDto pageableQueryDto) {
        Pageable pageable = pageableQueryDto.buildDefault();
        String profile = checkProfile();

        Page<ChatSession> pageRes = chatSessionRepo.findAll((root, query, cb) -> {
            List<Predicate> dynamicPredicates = new ArrayList<>();
            if (StringUtils.isNotBlank(agentCode)) {
                dynamicPredicates.add(cb.equal(root.get("agentCode"), agentCode));
            }
            if (BooleanUtils.isFalse(includeDeleted)) {
                dynamicPredicates.add(cb.equal(root.get("isDeleted"), false));
            }
            dynamicPredicates.add(cb.equal(root.get("profile"), profile));
            return cb.and(dynamicPredicates.toArray(new Predicate[0]));
        }, pageable);
        return PageRespDTO.of(pageRes, x -> BeanCopyUtil.copyPropertiesPlus(x, ChatSessionDto::new));
    }

    private static String checkProfile() {
        String profile = getProfile();
        if (StringUtils.isBlank(profile)) {
            throw new AuthException(SystemError.ACCESS_DENIED);
        }
        return profile;
    }

    public List<ChatConversationDto> listConversationsBySessionId(Long sessionId, SortQueryDto sortQueryDto) {
        checkSessionAndProfile(sessionId);

        Sort orders = sortQueryDto.buildDescByIdSort();
        List<ChatConversation> pageRes = chatConversationRepository.listBySessionId(sessionId, orders);
        return BeanCopyUtil.copyListProperties(pageRes, ChatConversationDto::new);
    }

    public PageRespDTO<ChatConversationDto> pageConversationsBySessionId(Long sessionId, PageableQueryDto pageableQueryDto) {
        checkSessionAndProfile(sessionId);

        Pageable pageable = pageableQueryDto.buildDescById();
        Page<ChatConversation> pageRes = chatConversationRepository.pageBySessionId(sessionId, pageable);
        return PageRespDTO.of(pageRes, x -> BeanCopyUtil.copyPropertiesPlus(x, ChatConversationDto::new));
    }

    public void checkSessionAndProfile(Long sessionId) {
        String profile = checkProfile();
        Optional<ChatSession> sessionOptional = chatSessionRepo.findById(sessionId);
        ChatSession sessionDto = sessionOptional.orElseThrow(() -> new IllegaInputException(SystemError.NOT_FOUND, "sessionId", sessionId));

        if (!profile.equals(sessionDto.getProfile())) {
            throw new AuthException(SystemError.ACCESS_DENIED);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ChatSessionDto createSession(String agentCode, @Valid ChatSessionEditDto chatSessionEditDto) {
        String sessionId = UUID.randomUUID().toString(true);
        ChatSession session = new ChatSession();
        session.setSessionId(sessionId);
        session.setProfile(getProfile());
        session.setAgentCode(agentCode);
        session.setContentSummary(chatSessionEditDto.getContentSummary());
        session.setUpdateTime(new Date());
        ChatSession save = chatSessionRepo.save(session);
        return BeanCopyUtil.copyPropertiesPlus(save, ChatSessionDto::new);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteSession(Long sessionId) {
        checkSessionAndProfile(sessionId);
        chatSessionRepo.updateAsDeleted(sessionId);
    }

    @Transactional(rollbackFor = Exception.class)
    public ChatSessionDto updateContentSummaryForSession(Long sessionId, ChatSessionEditDto contentSummary) {
        checkSessionAndProfile(sessionId);
        ChatSession session = chatSessionRepo.findById(sessionId).orElseThrow(() -> new IllegaInputException(SystemError.NOT_FOUND, "sessionId", sessionId));
        session.setContentSummary(contentSummary.getContentSummary());
        ChatSession save = chatSessionRepo.save(session);
        return BeanCopyUtil.copyPropertiesPlus(save, ChatSessionDto::new);
    }

    public List<ChatSessionDto> listSession(String agentCode, Boolean includeDeleted, SortQueryDto sortQueryDto) {
        Sort orders = sortQueryDto.buildDefaultSort();
        String profile = checkProfile();

        List<ChatSession> res = chatSessionRepo.findAll((root, query, cb) -> {
            List<Predicate> dynamicPredicates = new ArrayList<>();
            if (StringUtils.isNotBlank(agentCode)) {
                dynamicPredicates.add(cb.equal(root.get("agentCode"), agentCode));
            }
            if (BooleanUtils.isFalse(includeDeleted)) {
                dynamicPredicates.add(cb.equal(root.get("isDeleted"), false));
            }
            dynamicPredicates.add(cb.equal(root.get("profile"), profile));
            return cb.and(dynamicPredicates.toArray(new Predicate[0]));
        }, orders);
        return BeanCopyUtil.copyListProperties(res, ChatSessionDto::new);

    }


    public ConversationPairDto createConversationPair(ChatInputReqDto dto) {
        ChatConversation userMessage = saveUserMessage(dto);
        ChatConversation assiantMessage = createDraftAssiantMessage(dto.getChatSessionId());
        return new ConversationPairDto(userMessage.getId(), assiantMessage.getId());
    }

    private ChatConversation createDraftAssiantMessage(Long sessionId) {
        ChatConversation record = new ChatConversation();
        record.setMessageType(MessageType.ASSISTANT);
        //        record.setContent(dto.getContent());
        record.setChatSessionId(sessionId);
        record.setStatus(ConversationStatus.DRAFT);
        ChatConversation userMessage = chatConversationRepository.save(record);
        return userMessage;
    }

    private ChatConversation saveUserMessage(ChatInputReqDto dto) {
        ChatConversation record = new ChatConversation();
        record.setMessageType(MessageType.USER);
        record.setContent(dto.getContent());
        record.setChatSessionId(dto.getChatSessionId());
        record.setStatus(ConversationStatus.FORMAL);
        record.setMetadata(null);
        ChatConversation userMessage = chatConversationRepository.save(record);
        return userMessage;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteConversation(Long sessionId, Long conversationId) {
        checkSessionAndProfile(sessionId);
        chatConversationRepository.updateAsDeletedById(conversationId);
    }
}
