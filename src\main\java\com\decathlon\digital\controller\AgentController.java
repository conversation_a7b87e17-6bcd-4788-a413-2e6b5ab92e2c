package com.decathlon.digital.controller;

import com.decathlon.digital.common.dto.BaseApiResponseDto;
import com.decathlon.digital.common.exception.BaseException;
import com.decathlon.digital.config.SpringDocConfig;
import com.decathlon.digital.domain.chat.ChatRespDto;
import com.decathlon.digital.domain.agent.AgentInputReqDto;
import com.decathlon.digital.domain.agent.AiAgentDto;
import com.decathlon.digital.service.chat.AiAgentService;
import io.reactivex.Flowable;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @auther Shi Lei
 * @since 2025-05-06
 */
@Slf4j
@RestController
@Validated
@RequestMapping("api/v1/agents")
@RequiredArgsConstructor
@SecurityRequirement(name = SpringDocConfig.BEARER_AUTH)
public class AgentController {

    private final AiAgentService aiAgentService;

    @GetMapping(value = "")
    public BaseApiResponseDto<List<AiAgentDto>> getAllAgentList() {
        List<AiAgentDto> list = aiAgentService.getAllAgentList();
        return BaseApiResponseDto.ok(list);
    }

    @SneakyThrows
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flowable<BaseApiResponseDto<Object>> callAgentByStream(@RequestBody AgentInputReqDto dto) {

        Flowable<ChatRespDto> res = null;
        try {
            res = aiAgentService.callAgentByStream(dto);
            return (res.map(BaseApiResponseDto::ok));
        } catch (BaseException e) {
//            String fail = JacksonConverterUtil.convertObjectToJson());
            return Flowable.just(BaseApiResponseDto.fail(e.getCode(), e.getMessage()));
//                    .body(Flowable.just(fail));
        }
    }


}
