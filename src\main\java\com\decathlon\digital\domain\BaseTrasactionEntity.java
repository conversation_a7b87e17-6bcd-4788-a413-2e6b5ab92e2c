package com.decathlon.digital.domain;


import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Data;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @create 2018-05-30
 */
@MappedSuperclass
@Data
public abstract class BaseTrasactionEntity implements Serializable {

    private static final long serialVersionUID = -8674508403601592507L;


    @UpdateTimestamp
    @Column(name = "system_update_time", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date systemUpdateTime;
}
