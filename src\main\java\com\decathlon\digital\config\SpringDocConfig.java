package com.decathlon.digital.config;

import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@SecuritySchemes({
        @io.swagger.v3.oas.annotations.security.SecurityScheme(
                name = SpringDocConfig.BEARER_AUTH,
                type = SecuritySchemeType.HTTP,
                scheme = "bearer",
                bearerFormat = "JWT"
        )
})
public class SpringDocConfig {
    public static final String BEARER_AUTH = "BearerAuth";

    public OpenApiCustomizer openApiCustomiser() {
        return openApi -> {
            // 添加全局安全属性
            List<SecurityRequirement> securityRequirements = new ArrayList<>();
            securityRequirements.add(new SecurityRequirement().addList("basic"));
            openApi.security(securityRequirements);
        };
    }

    @Bean
    public OpenAPI openAPI(@Value("${server.servlet.context-path}") String contextPath) {
        return new OpenAPI()
                .addServersItem(new Server().url(contextPath))
                .components(new Components())
                .addSecurityItem(new SecurityRequirement().addList(BEARER_AUTH));
    }


}
